#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析运行时间数据的脚本
从日志文件中提取 [czcv_camera]run time: 后面的数字，并统计最大值和平均值
"""

import re
import statistics

def extract_runtime_data(file_path):
    """
    从文件中提取运行时间数据
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        list: 运行时间数据列表
    """
    runtime_values = []
    pattern = r'\[czcv_camera\]run time:\s*([0-9]+\.?[0-9]*)'
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                matches = re.findall(pattern, line)
                for match in matches:
                    try:
                        value = float(match)
                        runtime_values.append(value)
                        print(f"第{line_num}行找到运行时间: {value}")
                    except ValueError:
                        print(f"第{line_num}行无法转换为数字: {match}")
                        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []
    
    return runtime_values

def analyze_runtime_data(runtime_values):
    """
    分析运行时间数据
    
    Args:
        runtime_values (list): 运行时间数据列表
        
    Returns:
        dict: 包含统计信息的字典
    """
    if not runtime_values:
        return None
    
    max_value = max(runtime_values)
    min_value = min(runtime_values)
    avg_value = statistics.mean(runtime_values)
    median_value = statistics.median(runtime_values)
    count = len(runtime_values)
    
    return {
        'count': count,
        'max': max_value,
        'min': min_value,
        'average': avg_value,
        'median': median_value
    }

def plot_ascii_histogram(runtime_values, stats):
    """
    绘制ASCII字符直方图

    Args:
        runtime_values (list): 运行时间数据列表
        stats (dict): 统计信息字典
    """
    print(f"\n📊 运行时间分布直方图 (ASCII版本)")
    print("=" * 80)

    # 创建直方图数据
    min_val = stats['min']
    max_val = stats['max']
    n_bins = 20  # 分组数量

    # 计算每个区间的范围
    bin_width = (max_val - min_val) / n_bins
    bins = []
    counts = []

    for i in range(n_bins):
        bin_start = min_val + i * bin_width
        bin_end = min_val + (i + 1) * bin_width

        # 计算落在这个区间的数据点数量
        if i == n_bins - 1:  # 最后一个区间包含最大值
            count = sum(1 for x in runtime_values if bin_start <= x <= bin_end)
        else:
            count = sum(1 for x in runtime_values if bin_start <= x < bin_end)

        bins.append((bin_start, bin_end))
        counts.append(count)

    # 找到最大计数用于缩放
    max_count = max(counts) if counts else 1
    bar_width = 60  # ASCII条形图的最大宽度

    # 绘制直方图
    print(f"时间范围 (ms)      频次    百分比   直方图")
    print("-" * 80)

    for i, ((bin_start, bin_end), count) in enumerate(zip(bins, counts)):
        percentage = (count / len(runtime_values)) * 100
        bar_length = int((count / max_count) * bar_width) if max_count > 0 else 0
        bar = "█" * bar_length

        print(f"{bin_start:6.1f}-{bin_end:6.1f}   {count:6d}   {percentage:5.1f}%   {bar}")

    print("-" * 80)
    print(f"总数据点: {len(runtime_values)}")

    # 显示统计线位置
    print(f"\n📍 关键统计值位置:")
    print(f"  最小值: {stats['min']:.2f}ms")
    print(f"  平均值: {stats['average']:.2f}ms  ← 大部分数据的中心")
    print(f"  中位数: {stats['median']:.2f}ms   ← 50%数据分界点")
    print(f"  最大值: {stats['max']:.2f}ms")

def print_distribution_analysis(runtime_values, stats):
    """
    打印分布分析结果

    Args:
        runtime_values (list): 运行时间数据列表
        stats (dict): 统计信息字典
    """
    print(f"\n📈 详细分布分析:")
    print("=" * 50)

    # 计算百分位数
    percentiles = [10, 25, 50, 75, 90, 95, 99]
    print(f"百分位数分析:")
    for p in percentiles:
        # 手动计算百分位数
        sorted_values = sorted(runtime_values)
        index = int((p / 100.0) * (len(sorted_values) - 1))
        value = sorted_values[index]
        print(f"  P{p}: {value:.2f}ms")

    # 计算不同时间范围的数据分布
    ranges = [
        (0, 25, "< 25ms (快速)"),
        (25, 40, "25-40ms (正常)"),
        (40, 60, "40-60ms (较慢)"),
        (60, 80, "60-80ms (慢)"),
        (80, float('inf'), "> 80ms (很慢)")
    ]

    print(f"\n⏱️ 时间范围分布:")
    for min_val, max_val, label in ranges:
        if max_val == float('inf'):
            count = sum(1 for x in runtime_values if x >= min_val)
        else:
            count = sum(1 for x in runtime_values if min_val <= x < max_val)
        percentage = count / len(runtime_values) * 100
        print(f"  {label}: {count} 个 ({percentage:.1f}%)")

def main():
    """主函数"""
    file_path = r"D:\new 2.txt"

    print(f"正在分析文件: {file_path}")
    print("=" * 50)

    # 提取运行时间数据
    runtime_values = extract_runtime_data(file_path)

    if not runtime_values:
        print("没有找到任何运行时间数据")
        return

    print(f"\n总共找到 {len(runtime_values)} 个运行时间数据")
    print("=" * 50)

    # 分析数据
    stats = analyze_runtime_data(runtime_values)

    if stats:
        print(f"数据统计结果:")
        print(f"  数据总数: {stats['count']}")
        print(f"  最大值: {stats['max']:.6f}")
        print(f"  最小值: {stats['min']:.6f}")
        print(f"  平均值: {stats['average']:.6f}")
        print(f"  中位数: {stats['median']:.6f}")

        # 显示前10个和后10个数据作为样本
        print(f"\n前10个数据样本:")
        for i, value in enumerate(runtime_values[:10]):
            print(f"  {i+1}: {value}")

        if len(runtime_values) > 10:
            print(f"\n后10个数据样本:")
            for i, value in enumerate(runtime_values[-10:], len(runtime_values)-9):
                print(f"  {i}: {value}")

        # 打印详细分布分析
        print_distribution_analysis(runtime_values, stats)

        # 绘制ASCII直方图
        plot_ascii_histogram(runtime_values, stats)

if __name__ == "__main__":
    main()
