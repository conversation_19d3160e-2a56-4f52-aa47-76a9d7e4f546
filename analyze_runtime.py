#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析运行时间数据的脚本
从日志文件中提取 enTrackModeCustomMulti time: 后面的4个时间值，并统计每个时间的最大值、平均值和直方图
"""

import re
import statistics
import matplotlib.pyplot as plt
import numpy as np

def extract_runtime_data(file_path):
    """
    从文件中提取运行时间数据
    从每行中提取 enTrackModeCustomMulti time: 后面的4个时间值

    Args:
        file_path (str): 文件路径

    Returns:
        dict: 包含4个时间列表的字典 {'time1': [], 'time2': [], 'time3': [], 'time4': []}
    """
    time_data = {
        'time1': [],
        'time2': [],
        'time3': [],
        'time4': []
    }

    # 匹配 enTrackModeCustomMulti time: 后面的4个数字（用空格分隔）
    pattern = r'enTrackModeCustomMulti time:\s*([0-9]+\.?[0-9]*)\s+([0-9]+\.?[0-9]*)\s+([0-9]+\.?[0-9]*)\s+([0-9]+\.?[0-9]*)'

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                matches = re.findall(pattern, line)
                for match in matches:
                    try:
                        time1, time2, time3, time4 = [float(x) for x in match]
                        time_data['time1'].append(time1)
                        time_data['time2'].append(time2)
                        time_data['time3'].append(time3)
                        time_data['time4'].append(time4)
                        print(f"第{line_num}行找到时间数据: {time1}, {time2}, {time3}, {time4}")
                    except ValueError as e:
                        print(f"第{line_num}行数据转换错误: {match}, 错误: {e}")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return time_data
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return time_data

    return time_data

def analyze_time_data(time_values):
    """
    分析单个时间序列数据

    Args:
        time_values (list): 时间数据列表

    Returns:
        dict: 包含统计信息的字典
    """
    if not time_values:
        return None

    max_value = max(time_values)
    min_value = min(time_values)
    avg_value = statistics.mean(time_values)
    median_value = statistics.median(time_values)
    count = len(time_values)

    return {
        'count': count,
        'max': max_value,
        'min': min_value,
        'average': avg_value,
        'median': median_value
    }

def analyze_all_time_data(time_data):
    """
    分析所有时间序列数据

    Args:
        time_data (dict): 包含4个时间列表的字典

    Returns:
        dict: 包含每个时间序列统计信息的字典
    """
    results = {}
    for time_name, time_values in time_data.items():
        results[time_name] = analyze_time_data(time_values)
    return results

def plot_ascii_histogram(time_values, stats, time_name):
    """
    绘制ASCII字符直方图

    Args:
        time_values (list): 时间数据列表
        stats (dict): 统计信息字典
        time_name (str): 时间序列名称
    """
    if not time_values or not stats:
        print(f"\n⚠️ {time_name} 没有数据可显示")
        return

    print(f"\n📊 {time_name} 时间分布直方图 (ASCII版本)")
    print("=" * 80)

    # 创建直方图数据
    min_val = stats['min']
    max_val = stats['max']
    n_bins = 20  # 分组数量

    # 如果最大值和最小值相同，特殊处理
    if min_val == max_val:
        print(f"所有数据都是相同值: {min_val:.2f}")
        return

    # 计算每个区间的范围
    bin_width = (max_val - min_val) / n_bins
    bins = []
    counts = []

    for i in range(n_bins):
        bin_start = min_val + i * bin_width
        bin_end = min_val + (i + 1) * bin_width

        # 计算落在这个区间的数据点数量
        if i == n_bins - 1:  # 最后一个区间包含最大值
            count = sum(1 for x in time_values if bin_start <= x <= bin_end)
        else:
            count = sum(1 for x in time_values if bin_start <= x < bin_end)

        bins.append((bin_start, bin_end))
        counts.append(count)

    # 找到最大计数用于缩放
    max_count = max(counts) if counts else 1
    bar_width = 60  # ASCII条形图的最大宽度

    # 绘制直方图
    print(f"时间范围          频次    百分比   直方图")
    print("-" * 80)

    for i, ((bin_start, bin_end), count) in enumerate(zip(bins, counts)):
        percentage = (count / len(time_values)) * 100
        bar_length = int((count / max_count) * bar_width) if max_count > 0 else 0
        bar = "█" * bar_length

        print(f"{bin_start:6.1f}-{bin_end:6.1f}   {count:6d}   {percentage:5.1f}%   {bar}")

    print("-" * 80)
    print(f"总数据点: {len(time_values)}")

    # 显示统计线位置
    print(f"\n📍 {time_name} 关键统计值:")
    print(f"  最小值: {stats['min']:.2f}")
    print(f"  平均值: {stats['average']:.2f}  ← 大部分数据的中心")
    print(f"  中位数: {stats['median']:.2f}   ← 50%数据分界点")
    print(f"  最大值: {stats['max']:.2f}")

def print_distribution_analysis(time_values, time_name):
    """
    打印分布分析结果

    Args:
        time_values (list): 时间数据列表
        time_name (str): 时间序列名称
    """
    if not time_values:
        print(f"\n⚠️ {time_name} 没有数据可分析")
        return

    print(f"\n📈 {time_name} 详细分布分析:")
    print("=" * 50)

    # 计算百分位数
    percentiles = [10, 25, 50, 75, 90, 95, 99]
    print(f"百分位数分析:")
    for p in percentiles:
        # 手动计算百分位数
        sorted_values = sorted(time_values)
        index = int((p / 100.0) * (len(sorted_values) - 1))
        value = sorted_values[index]
        print(f"  P{p}: {value:.2f}")

    # 计算不同时间范围的数据分布（根据数据范围动态调整）
    min_val = min(time_values)
    max_val = max(time_values)
    range_size = (max_val - min_val) / 5

    ranges = []
    for i in range(5):
        start = min_val + i * range_size
        end = min_val + (i + 1) * range_size if i < 4 else float('inf')
        if i == 4:
            label = f">= {start:.1f} (最高)"
        else:
            label = f"{start:.1f}-{end:.1f}"
        ranges.append((start, end, label))

    print(f"\n⏱️ 时间范围分布:")
    for min_range, max_range, label in ranges:
        if max_range == float('inf'):
            count = sum(1 for x in time_values if x >= min_range)
        else:
            count = sum(1 for x in time_values if min_range <= x < max_range)
        percentage = count / len(time_values) * 100
        print(f"  {label}: {count} 个 ({percentage:.1f}%)")

def main():
    """主函数"""
    file_path = r"D:\new 3.txt"

    print(f"正在分析文件: {file_path}")
    print("分析 enTrackModeCustomMulti time: 后面的4个时间值")
    print("=" * 60)

    # 提取时间数据
    time_data = extract_runtime_data(file_path)

    # 检查是否有数据
    total_records = len(time_data['time1'])
    if total_records == 0:
        print("没有找到任何 enTrackModeCustomMulti time: 数据")
        return

    print(f"\n总共找到 {total_records} 条记录")
    print("=" * 60)

    # 分析每个时间序列
    all_stats = analyze_all_time_data(time_data)

    # 显示统计结果
    print(f"\n📊 各时间序列统计结果:")
    print("=" * 60)

    time_names = ['time1', 'time2', 'time3', 'time4']
    for time_name in time_names:
        stats = all_stats[time_name]
        if stats:
            print(f"\n{time_name.upper()} 统计:")
            print(f"  数据总数: {stats['count']}")
            print(f"  最大值: {stats['max']:.6f}")
            print(f"  最小值: {stats['min']:.6f}")
            print(f"  平均值: {stats['average']:.6f}")
            print(f"  中位数: {stats['median']:.6f}")

    # 显示数据样本
    print(f"\n📋 前10条记录样本:")
    print("-" * 60)
    print("序号    Time1      Time2      Time3      Time4")
    print("-" * 60)
    for i in range(min(10, total_records)):
        print(f"{i+1:3d}   {time_data['time1'][i]:8.3f}   {time_data['time2'][i]:8.3f}   {time_data['time3'][i]:8.3f}   {time_data['time4'][i]:8.3f}")

    if total_records > 10:
        print(f"\n📋 后10条记录样本:")
        print("-" * 60)
        print("序号    Time1      Time2      Time3      Time4")
        print("-" * 60)
        for i in range(max(0, total_records-10), total_records):
            print(f"{i+1:3d}   {time_data['time1'][i]:8.3f}   {time_data['time2'][i]:8.3f}   {time_data['time3'][i]:8.3f}   {time_data['time4'][i]:8.3f}")

    # 为每个时间序列生成详细分析和直方图
    for time_name in time_names:
        time_values = time_data[time_name]
        stats = all_stats[time_name]

        if time_values and stats:
            # 打印详细分布分析
            print_distribution_analysis(time_values, time_name)

            # 绘制ASCII直方图
            plot_ascii_histogram(time_values, stats, time_name)

if __name__ == "__main__":
    main()
