import numpy as np
import math
from dataclasses import dataclass
from scipy.optimize import least_squares

# ----------------- 旋转工具（角度制） -----------------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation (optical 轴初始沿 +Z，右手系)
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# ----------------- 内参计算函数 -----------------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

def intrinsics_from_hfov(w, h, hfov_deg, cx, cy):
    fovx = math.radians(hfov_deg)
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# ----------------- 固定参数：只包含不随优化变化的量 -----------------
@dataclass
class RigFixed:
    Kp: np.ndarray      # 投影仪内参
    Kc: np.ndarray      # 摄像头内参
    dx: float           # 摄像头相对投影仪在 X 方向的基线 (m)
    R_cam_rel: np.ndarray  # 摄像头相对投影仪的固定旋转（cam->proj，rig=proj）

# ----------------- 在投影仪 UI 上生成棋盘格点 -----------------
def make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=200):
    """
    在投影仪图像上生成 nx * ny 个角点 (u_p, v_p)
    """
    xs = np.linspace(margin, Wp - margin, nx)
    ys = np.linspace(margin, Hp - margin, ny)
    uu, vv = np.meshgrid(xs, ys)
    pts = np.vstack([uu.ravel(), vv.ravel()]).T   # (N,2)
    return pts

# ----------------- 前向模型：给定 (pitch,yaw,roll,D)，预测摄像头像素 -----------------
def forward_project_theta(theta, fixed: RigFixed, proj_pts):
    """
    theta: (4,) -> [pitch, yaw, roll, D]
    proj_pts: (N,2) 投影仪 UI 上棋盘格角点像素 (u_p, v_p)
    fixed: 包含 Kp, Kc, dx, R_cam_rel

    返回: cam_pred (N,2) 摄像头预测像素坐标
    """
    pitch, yaw, roll, D = theta
    Kp, Kc, dx, R_cam_rel = fixed.Kp, fixed.Kc, fixed.dx, fixed.R_cam_rel

    # 1) 整机（rig）相对于墙的旋转：rig c2w
    R_rig = build_rotation(pitch, yaw, roll)

    # 2) 投影仪的 camera->world 旋转：rig 坐标 = 投影仪坐标
    R_proj_c2w = R_rig

    # 3) 摄像头的 camera->world 旋转
    #    cam_c2w = rig_c2w @ cam_rel_c2rig
    R_cam_c2w = R_rig @ R_cam_rel

    # 4) 投影仪、摄像头中心（世界坐标）
    #    世界坐标系：墙是 Z=0，rig 原点在 (0,0,-D)，Z 轴指向 +Z（墙）
    C_proj_w = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)
    # 摄像头在 rig 坐标中的位置： (dx, 0, 0)
    C_cam_rig = np.array([dx, 0.0, 0.0], dtype=np.float64).reshape(3,1)
    C_cam_w = C_proj_w + R_rig @ C_cam_rig   # (3,1)

    # 5) 投影仪像素 -> 投影仪相机坐标射线
    N = proj_pts.shape[0]
    u_p = proj_pts[:, 0]
    v_p = proj_pts[:, 1]

    uv1 = np.vstack([u_p, v_p, np.ones_like(u_p)])   # (3,N)
    Kp_inv = np.linalg.inv(Kp)
    d_pc = Kp_inv @ uv1                              # (3,N) 投影仪相机坐标系下射线方向

    # 6) 旋转到世界坐标系下的射线方向
    d_pw = R_proj_c2w @ d_pc                         # (3,N)
    dxw, dyw, dzw = d_pw

    # 7) 和墙面 Z=0 的交点
    #    P_w(t) = C_proj_w + t * d_pw
    #    Z: -D + t * dzw = 0  -> t = D / dzw
    eps = 1e-9
    dzw_safe = np.where(np.abs(dzw) < eps, eps, dzw)
    t = D / dzw_safe

    P_w = C_proj_w + d_pw * t  # (3,N)
    P_w = P_w.T                # (N,3)

    # 8) 世界 -> 摄像头坐标，再投影到像素
    R_w2c = R_cam_c2w.T
    Pw = P_w.T                                # (3,N)
    Pc = R_w2c @ (Pw - C_cam_w)               # (3,N)
    Xc, Yc, Zc = Pc

    Zc_safe = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u_c = fx * (Xc / Zc_safe) + cx
    v_c = fy * (Yc / Zc_safe) + cy

    cam_pred = np.vstack([u_c, v_c]).T        # (N,2)
    return cam_pred

# ----------------- 残差函数：用于最小二乘优化 (pitch,yaw,roll,D) -----------------
def residual_params(theta, fixed: RigFixed, proj_pts, cam_obs):
    """
    theta: (4,) -> [pitch, yaw, roll, D]
    proj_pts: (N,2) 投影仪像素
    cam_obs:  (N,2) 摄像头观测像素
    返回: (2N,) 残差向量
    """
    cam_pred = forward_project_theta(theta, fixed, proj_pts)   # (N,2)
    res = (cam_pred - cam_obs).ravel()
    return res

# ----------------- 主函数：模拟 + 优化 -----------------
def main():
    # ---- 1. 定义分辨率与内参 ----
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp / 2.0, Hp    # 主点在底边中心

    Wc, Hc = 1920, 1080
    hfov_deg = 75.0
    cxc, cyc = Wc / 2.0, Hc / 2.0

    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    Kc = intrinsics_from_hfov(Wc, Hc, hfov_deg, cxc, cyc)

    # ---- 2. 几何参数：基线 ----
    dx = 0.1           # 摄像头相对投影仪在 X 方向的基线（已知）

    # ---- 3. 正投时已标定出的相机相对投影仪姿态 ----
    #     举例：cam 相对 proj pitch=8°, yaw=0, roll=0
    cam_rel_pitch = 8.0
    cam_rel_yaw   = 0.0
    cam_rel_roll  = 0.0
    R_cam_rel = build_rotation(cam_rel_pitch, cam_rel_yaw, cam_rel_roll)

    fixed = RigFixed(
        Kp=Kp,
        Kc=Kc,
        dx=dx,
        R_cam_rel=R_cam_rel
    )

    # ---- 4. 设定“真值整机姿态”和“真值距离 D” ----
    pitch_true = 4
    yaw_true   = -7.0
    roll_true  = 1.0
    D_true     = 1.5    # 真值距墙距离

    theta_true = np.array([pitch_true, yaw_true, roll_true, D_true], dtype=np.float64)

    print("Ground truth [pitch, yaw, roll, D]:", theta_true)
    print(f"Camera relative to projector (deg): pitch={cam_rel_pitch}, yaw={cam_rel_yaw}, roll={cam_rel_roll}")
    print("Baseline dx (m):", dx)

    # ---- 5. 在投影仪 UI 上生成棋盘格角点 ----
    proj_pts = make_projector_chessboard(Wp, Hp, nx=8, ny=8, margin=250)
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    # ---- 6. 用真值参数生成摄像头观测 cam_obs ----
    cam_obs = forward_project_theta(theta_true, fixed, proj_pts)

    # 可以加一点噪声，模拟棋盘角点检测误差
    noise_std = 0.2   # 像素
    cam_obs_noisy = cam_obs + np.random.randn(*cam_obs.shape) * noise_std

    # ---- 7. 优化：从一个初始猜测开始恢复 (pitch,yaw,roll,D) ----
    theta0 = np.array([0.0, 0.0, 0.0, 1.5], dtype=np.float64)   # 初值
    print("Initial guess [pitch, yaw, roll, D]:", theta0)

    # 给个合理的角度 & D 范围
    lower = np.array([-30.0, -30.0, -2.0, 0.5])
    upper = np.array([ 30.0,  30.0,  2.0, 4.0])

    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs_noisy),
        method="trf",
        bounds=(lower, upper),
        verbose=1
    )

    theta_est = result.x
    print("\nEstimated [pitch, yaw, roll, D]:", theta_est)
    print("Angle errors (deg):", theta_est[:3] - theta_true[:3])
    print("D error (m):", theta_est[3] - D_true)

    # ---- 8. 看一下用估计参数对“无噪声真值”做重投影误差 ----
    cam_pred_est = forward_project_theta(theta_est, fixed, proj_pts)
    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print("\nMean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

if __name__ == "__main__":
    main()