import json

import numpy as np
import math
from dataclasses import dataclass
from scipy.optimize import least_squares



def get_corners_from_left_top(width, height, square_w, square_h, squares_x, squares_y):
    """
    按 从上到下、每行从左到右 的顺序生成角点坐标。
    这里角点是所有网格交点，因此有 (squares_x+1) * (squares_y+1) 个角点。
    """
    corners = []
    for j in range(squares_y + 1):         # 从上到下
        for i in range(squares_x + 1):     # 每行从左到右
            x = i * square_w
            y = j * square_h
            # 防止落在 width / height 边界之外（最后一个点可能等于 width / height）
            x = min(x, width - 1)
            y = min(y, height - 1)
            corners.append((x, y))
    return corners

def get_proj_pts():
    width, height = 1920, 1080
    squares_x, squares_y = 12, 8
    square_w = width // squares_x
    square_h = height // squares_y

    # 生成角点（从左到右，从上到下）
    corners = get_corners_from_left_top(
        width, height,
        square_w, square_h,
        squares_x, squares_y
    )
    corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    return corners


# ----------------- 旋转工具（角度制） -----------------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation (光轴初始沿 +Z，右手系)
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# ----------------- 内参计算函数 -----------------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    # 用投射比估算投影仪内参
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

def intrinsics_from_hfov(w, h, hfov_deg, cx, cy):
    # 用水平视场估算摄像头内参
    fovx = math.radians(hfov_deg)
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# ----------------- 固定参数 -----------------
@dataclass
class RigFixed:
    Kp: np.ndarray      # 投影仪内参
    Kc: np.ndarray      # 摄像头内参
    dx: float           # 摄像头相对投影仪在 X 方向的基线 (m)
    R_cam_rel: np.ndarray  # 摄像头相对投影仪的固定旋转（cam->proj，rig=proj）

# ----------------- 在投影仪 UI 上生成棋盘格点 -----------------
def make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=200):
    """
    在投影仪图像上生成 nx * ny 个角点 (u_p, v_p)
    """
    xs = np.linspace(margin, Wp - margin, nx)
    ys = np.linspace(margin, Hp - margin, ny)
    uu, vv = np.meshgrid(xs, ys)
    pts = np.vstack([uu.ravel(), vv.ravel()]).T   # (N,2)
    return pts

# ----------------- forward: 给定 rig 姿态 -> 摄像头像素 -----------------
def forward_project_theta(theta, fixed: RigFixed, proj_pts):
    """
    theta: (3,) -> [pitch, yaw, roll]  （rig 姿态）
    D 使用 fixed.D0
    proj_pts: (N,2) 投影仪 UI 上角点像素 (u_p, v_p)
    返回: cam_pred (N,2) 摄像头像素坐标
    """
    pitch, yaw, roll, D = theta
    Kp, Kc, dx, R_cam_rel = fixed.Kp, fixed.Kc, fixed.dx, fixed.R_cam_rel

    # 1) 整机（rig）相对于墙的旋转：rig c2w
    R_rig = build_rotation(pitch, yaw, roll)

    # 2) 投影仪的 camera->world 旋转：rig 坐标 = 投影仪坐标
    R_proj_c2w = R_rig

    # 3) 摄像头的 camera->world 旋转
    #    cam_c2w = rig_c2w @ cam_rel_c2rig
    R_cam_c2w = R_rig @ R_cam_rel

    # 4) 投影仪、摄像头中心（世界坐标）
    #    世界坐标系：墙是 Z=0，rig 原点在 (0,0,-D)，Z 轴指向 +Z（墙）
    C_proj_w = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)
    # 摄像头在 rig 坐标中的位置： (dx, 0, 0)
    C_cam_rig = np.array([dx, 0.0, 0.0], dtype=np.float64).reshape(3,1)
    C_cam_w = C_proj_w + R_rig @ C_cam_rig   # (3,1)

    # 5) 投影仪像素 -> 投影仪相机坐标射线
    N = proj_pts.shape[0]
    u_p = proj_pts[:, 0]
    v_p = proj_pts[:, 1]

    uv1 = np.vstack([u_p, v_p, np.ones_like(u_p)])   # (3,N)
    Kp_inv = np.linalg.inv(Kp)
    d_pc = Kp_inv @ uv1                              # (3,N) 投影仪相机坐标系下射线方向

    # 6) 旋转到世界坐标系下的射线方向
    d_pw = R_proj_c2w @ d_pc                         # (3,N)
    dxw, dyw, dzw = d_pw

    # 7) 和墙面 Z=0 的交点
    #    P_w(t) = C_proj_w + t * d_pw
    #    Z: -D + t * dzw = 0  -> t = D / dzw
    eps = 1e-9
    dzw_safe = np.where(np.abs(dzw) < eps, eps, dzw)
    t = D / dzw_safe

    P_w = C_proj_w + d_pw * t  # (3,N)
    P_w = P_w.T                # (N,3)

    # 8) 世界 -> 摄像头坐标，再投影到像素
    R_w2c = R_cam_c2w.T
    Pw = P_w.T                                # (3,N)
    Pc = R_w2c @ (Pw - C_cam_w)               # (3,N)
    Xc, Yc, Zc = Pc

    Zc_safe = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u_c = fx * (Xc / Zc_safe) + cx
    v_c = fy * (Yc / Zc_safe) + cy

    cam_pred = np.vstack([u_c, v_c]).T        # (N,2)
    return cam_pred

# ----------------- 残差：只用“角点差值”拟合姿态 -----------------
def residual_from_delta(theta_side, fixed: RigFixed, proj_pts, delta_obs):
    """
    theta_side: (3,) -> [pitch, yaw, roll]  侧投时的整机姿态（我们要求的）
    fixed.D0: 正投时的距离，也用于侧投（假设不变）

    delta_obs: (N,2) = cam_side_obs - cam_front_obs （实测差值）

    残差: (2N,) = (cam_side_pred - cam_front_pred) - delta_obs
    """
    # 正投姿态：rig = [0,0,0]
    theta_front = np.array([0.0, 0.0, 0.0, 1.98], dtype=np.float64)

    cam_front_pred = forward_project_theta(theta_front, fixed, proj_pts)  # (N,2)
    cam_side_pred  = forward_project_theta(theta_side, fixed, proj_pts)   # (N,2)

    delta_pred = cam_side_pred - cam_front_pred

    res = (delta_pred - delta_obs).ravel()
    return res


def get_cam_K():
    return np.array(
        [[813.13991884, 0,             573.13396352],
         [0,            822.02481376,  408.75756843],
         [0, 0, 1],
         ])
    # return  np.array(
    #     [[838.295, 0,        618.496],
    #      [0,       851.146,  403.593],
    #      [0,       0,        1],
    #     ])

def get_projector_K():
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp / 2.0, Hp
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    return Kp


def  get_point(fn='data/cam_720p_ang0.json'):
    j = json.load(open(fn))
    p_list = []
    for s in j['shapes']:
        p = s['points'][0]
        p_list.append(p)
    return p_list

def main():
    proj_pts = get_proj_pts()
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    Kp = get_projector_K()
    Kc = get_cam_K()

    # ---- 2. 几何参数：dx + 正投时 cam_rel ----
    dx = - 0.1           # 摄像头相对投影仪在 X 方向的基线（已知）

    # 正投标定好的：摄像头相对光机姿态（cam_rel）
    cam_rel_pitch = 15.94633358861443
    cam_rel_yaw   = -2.4718559450382855
    cam_rel_roll  = -1.872923249794174
    R_cam_rel = build_rotation(cam_rel_pitch, cam_rel_yaw, cam_rel_roll)


    fixed = RigFixed(
        Kp=Kp,
        Kc=Kc,
        dx=dx,
        R_cam_rel=R_cam_rel,
    )

    cam_front_obs = get_point(fn='data/cam_720p_ang0.json')
    cam_front_obs = np.asarray(cam_front_obs)

    #cam_side = get_point(fn='data/cam_720p_yaw.json')
    cam_side = get_point(fn='data/cam_720p_pitch.json')

    cam_side = np.asarray(cam_side)

    # 你在真实系统里拿到的就是这个差值：
    delta_obs = cam_side - cam_front_obs

    # ---- 5. 用“差值”反求侧投姿态 ----
    theta_side0 = np.array([0.0, 0.0, 0.0, 1.98], dtype=np.float64)  # 初猜：还在正投

    lower = np.array([-30.0, -30.0, -10.0, 0.5], dtype=np.float64)
    upper = np.array([ 30.0,  30.0,  10.0, 3.0], dtype=np.float64)

    result = least_squares(
        residual_from_delta,
        theta_side0,
        args=(fixed, proj_pts, delta_obs),
        method="trf",
        bounds=(lower, upper),
        verbose=1
    )

    theta_side_est = result.x

    print("\nEstimated side pose (pitch,yaw,roll) =", theta_side_est)

    # ---- 6. 看一下用估计姿态对“无噪声真值”的重投影误差 ----
    cam_side_est = forward_project_theta(theta_side_est, fixed, proj_pts)
    reproj_err = np.linalg.norm(cam_side_est - cam_side, axis=1)
    print("\nReprojection error wrt true side pose [pixels]:")
    print("  mean =", reproj_err.mean())
    print("  max  =", reproj_err.max())


if __name__ == "__main__":
    main()