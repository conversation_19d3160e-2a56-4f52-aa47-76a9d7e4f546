#!/usr/bin/env python3
"""
纯文本分析棋盘格角点检测结果
"""
import numpy as np
import os

def analyze_corners():
    """分析角点检测结果"""
    print("棋盘格角点检测结果分析")
    print("=" * 50)
    
    # 读取角点坐标
    corners_file = "chessboard_corners_advanced.txt"
    if not os.path.exists(corners_file):
        print(f"角点文件不存在: {corners_file}")
        return
    
    # 读取角点坐标
    corners = np.loadtxt(corners_file, skiprows=2)
    print(f"成功读取 {len(corners)} 个角点坐标")
    
    # 显示所有角点坐标
    print("\n检测到的棋盘格角点坐标:")
    print("-" * 50)
    for i, (x, y) in enumerate(corners):
        print(f"角点 {i+1:2d}: ({x:7.2f}, {y:7.2f})")
    
    # 计算角点统计信息
    x_coords = corners[:, 0]
    y_coords = corners[:, 1]
    
    print(f"\n角点统计信息:")
    print("-" * 50)
    print(f"总角点数: {len(corners)}")
    print(f"X坐标范围: {x_coords.min():.2f} - {x_coords.max():.2f} (跨度: {x_coords.max() - x_coords.min():.2f})")
    print(f"Y坐标范围: {y_coords.min():.2f} - {y_coords.max():.2f} (跨度: {y_coords.max() - y_coords.min():.2f})")
    print(f"X坐标平均值: {x_coords.mean():.2f} ± {x_coords.std():.2f}")
    print(f"Y坐标平均值: {y_coords.mean():.2f} ± {y_coords.std():.2f}")
    
    # 分析棋盘格结构 (5x4 模式)
    print(f"\n棋盘格结构分析 (5x4 模式):")
    print("-" * 50)
    
    # 重新排列角点为5x4网格
    corners_grid = corners.reshape(4, 5, 2)
    
    print("网格结构 (行 x 列):")
    for row in range(4):
        print(f"第 {row+1} 行:", end=" ")
        for col in range(5):
            x, y = corners_grid[row, col]
            print(f"({x:.1f},{y:.1f})", end=" ")
        print()
    
    # 计算行间距和列间距
    col_spacings = []
    row_spacings = []
    
    # 计算列间距 (水平方向)
    print(f"\n列间距分析 (水平方向):")
    for row in range(4):
        row_col_spacings = []
        for col in range(4):  # 5列，4个间距
            dx = corners_grid[row, col+1, 0] - corners_grid[row, col, 0]
            col_spacings.append(dx)
            row_col_spacings.append(dx)
        print(f"第 {row+1} 行列间距: {np.mean(row_col_spacings):.2f} ± {np.std(row_col_spacings):.2f}")
    
    # 计算行间距 (垂直方向)
    print(f"\n行间距分析 (垂直方向):")
    for row in range(3):  # 4行，3个间距
        row_row_spacings = []
        for col in range(5):
            dy = corners_grid[row+1, col, 1] - corners_grid[row, col, 1]
            row_spacings.append(dy)
            row_row_spacings.append(dy)
        print(f"第 {row+1}-{row+2} 行间距: {np.mean(row_row_spacings):.2f} ± {np.std(row_row_spacings):.2f}")
    
    print(f"\n总体间距统计:")
    print(f"平均列间距 (水平): {np.mean(col_spacings):.2f} ± {np.std(col_spacings):.2f} 像素")
    print(f"平均行间距 (垂直): {np.mean(row_spacings):.2f} ± {np.std(row_spacings):.2f} 像素")
    
    # 计算棋盘格的几何特性
    print(f"\n棋盘格几何特性:")
    print("-" * 50)
    
    # 计算棋盘格的总尺寸
    total_width = x_coords.max() - x_coords.min()
    total_height = y_coords.max() - y_coords.min()
    print(f"棋盘格总尺寸: {total_width:.2f} x {total_height:.2f} 像素")
    
    # 计算棋盘格中心
    center_x = (x_coords.max() + x_coords.min()) / 2
    center_y = (y_coords.max() + y_coords.min()) / 2
    print(f"棋盘格中心: ({center_x:.2f}, {center_y:.2f})")
    
    # 检查棋盘格的规整性
    col_spacing_cv = np.std(col_spacings) / np.mean(col_spacings) * 100
    row_spacing_cv = np.std(row_spacings) / np.mean(row_spacings) * 100
    
    print(f"\n规整性分析:")
    print(f"列间距变异系数: {col_spacing_cv:.2f}% ({'良好' if col_spacing_cv < 5 else '一般' if col_spacing_cv < 10 else '较差'})")
    print(f"行间距变异系数: {row_spacing_cv:.2f}% ({'良好' if row_spacing_cv < 5 else '一般' if row_spacing_cv < 10 else '较差'})")
    
    # 检查生成的文件
    print(f"\n生成的文件:")
    print("-" * 50)
    
    files_to_check = [
        "chessboard_corners_advanced.txt",
        "chessboard_detection_results/chessboard_blurred_5x4.jpg",
        "chessboard_detection_results/chessboard_equalized_5x4.jpg",
        "chessboard_detection_results/01_original.jpg",
        "chessboard_detection_results/02_gray.jpg",
        "chessboard_detection_results/03_blurred.jpg",
        "chessboard_detection_results/04_equalized.jpg",
        "chessboard_detection_results/05_adaptive_thresh.jpg"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            if file_path.endswith('.txt'):
                size = os.path.getsize(file_path)
                print(f"✓ {file_path} ({size} 字节)")
            else:
                print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (不存在)")
    
    print(f"\n分析完成！")
    print("检测成功找到了一个 5x4 的棋盘格内角点模式")
    print("这意味着原始棋盘格是 6x5 的格子模式")

if __name__ == "__main__":
    analyze_corners()
