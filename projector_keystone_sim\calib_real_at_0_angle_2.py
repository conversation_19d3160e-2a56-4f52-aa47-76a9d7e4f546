import json
import math
from dataclasses import dataclass

import cv2
import numpy as np

from scipy.optimize import least_squares


def get_corners_from_left_top(width, height, square_w, square_h, squares_x, squares_y):
    """
    按 从上到下、每行从左到右 的顺序生成角点坐标。
    这里角点是所有网格交点，因此有 (squares_x+1) * (squares_y+1) 个角点。
    """
    corners = []
    for j in range(squares_y + 1):         # 从上到下
        for i in range(squares_x + 1):     # 每行从左到右
            x = i * square_w
            y = j * square_h
            # 防止落在 width / height 边界之外（最后一个点可能等于 width / height）
            x = min(x, width - 1)
            y = min(y, height - 1)
            corners.append((x, y))
    return corners


def get_proj_pts():
    width, height = 1920, 1080
    squares_x, squares_y = 12, 8
    square_w = width // squares_x
    square_h = height // squares_y

    # 生成角点（从左到右，从上到下）
    corners = get_corners_from_left_top(
        width, height,
        square_w, square_h,
        squares_x, squares_y
    )
    corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    # 去掉外圈，只保留棋盘内部角点
    corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    return corners


# ---------- 旋转工具 ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)


def get_point(fn='data/cam_720p_ang0.json'):
    j = json.load(open(fn))
    p_list = []
    for s in j['shapes']:
        p = s['points'][0]
        p_list.append(p)
    return p_list


def get_cam_K():
    # 作为初始值，用标定得到的 Kc0
    # return np.array(
    #     [[820.295, 0,   601.496],
    #      [0,      821.146, 403.593],
    #      [0,      0,   1]],
    #     dtype=np.float64
    # )
    return  np.array(
        [[838.295, 0,        618.496],
         [0,       851.146,  403.593],
         [0,       0,        1],
        ])


# ---------- 投影仪内参 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)


def get_projector_K():
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp / 2.0, Hp
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    return Kp


# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, Kp, D):
    """
    u, v: (N,) 投影仪像素坐标
    D: 投影中心到墙面的距离 (m)
    返回: (N,3) 世界坐标点 (X,Y,0)
    """
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)
    dx, dy, dz = d

    # 投影仪光心
    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    # 与 Z=0 平面交点
    t = D / dz
    P = Cp + d * t      # (3,N)
    return P.T          # (N,3)


# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, Kc, dx, D, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    dx: 相机中心相对投影仪在 X 方向上的基线 (m)
    D:  与墙面的距离 (决定相机的 Z 坐标)
    返回: (N,2) 相机像素坐标
    """
    # 相机中心（与投影仪同 z = -D，x 上有基线 dx）
    C_cam = np.array([dx, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy
    return np.vstack([u, v]).T   # (N,2)


# ---------- 固定参数：投影仪内参 + 基线 + 相机初始内参 ----------
@dataclass
class SystemFixed:
    Kp: np.ndarray      # 投影仪内参（固定）
    Kc0: np.ndarray     # 相机初始内参（用来加增量）
    dx: float           # 相机相对投影仪在 X 方向上的基线 (米)


# ---------- 残差函数：同时优化 姿态 + D + 相机内参 ----------
def residual_params(theta, fixed: SystemFixed, proj_pts, cam_obs):
    """
    theta: [pitch, yaw, roll, D, d_fx, d_fy, d_cx, d_cy]
    fixed: 包含 Kp, Kc0, dx
    proj_pts: (N,2)
    cam_obs:  (N,2)
    返回 (2N,) 残差
    """
    pitch, yaw, roll, D, d_fx, d_fy, d_cx, d_cy = theta
    #D = 2.0
    Kp, Kc0, dx = fixed.Kp, fixed.Kc0, fixed.dx

    # 从增量恢复当前相机内参
    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    fx = fx0 + d_fx
    fy = fy0 + d_fy
    cx = cx0 + d_cx
    cy = cy0 + d_cy

    Kc = np.array([[fx, 0.0, cx],
                   [0.0, fy, cy],
                   [0.0, 0.0, 1.0]], dtype=np.float64)

    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(u_p, v_p, Kp, D)  # (N,3)

    # 墙面 3D -> 相机像素
    cam_pred = world_to_camera_pixel(Pw, Kc, dx, D, pitch, yaw, roll)  # (N,2)

    res = (cam_pred - cam_obs).ravel()
    return res


if __name__ == '__main__':

    proj_pts = get_proj_pts()
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    Kp = get_projector_K()
    Kc0 = get_cam_K()
    dx = -0.01


    # 读取真实相机观测
    image = cv2.imread('./data/cam_720p_ang0.jpg')
    cam_obs = np.asarray(get_point())   # (N,2)

    # ---------- 初始猜测 ----------
    # 姿态先从 0 开始，D 用 2m 作为初值，相机内参增量从 0 开始
    theta0 = np.array([12.0, 0.0, 0.0,  # pitch, yaw, roll
                       2.0,            # D
                       0.0, 0.0,       # d_fx, d_fy
                       0.0, 0.0],      # d_cx, d_cy
                      dtype=np.float64)
    print("Initial guess [pitch,yaw,roll,D, d_fx,d_fy,d_cx,d_cy]:", theta0)

    # ---------- 约束 ----------
    # 姿态角：[-30, 30] 度
    pitch_min, pitch_max = -30.0, 30.0
    yaw_min,   yaw_max   = -30.0, 30.0
    roll_min,  roll_max  = -30.0, 30.0

    # 距离 D: [0.5m, 4m]
    D_min, D_max = 1.98, 2.01

    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    # 焦距允许 ±20%
    dfx_min, dfx_max = -0.2 * fx0, 0.2 * fx0
    dfy_min, dfy_max = -0.2 * fy0, 0.2 * fy0

    # 主点允许偏移 ±200 像素
    dcx_min, dcx_max = -200.0, 200.0
    dcy_min, dcy_max = -200.0, 200.0

    lower = np.array([
        pitch_min, yaw_min, roll_min,
        D_min,
        dfx_min, dfy_min,
        dcx_min, dcy_min
    ], dtype=np.float64)

    upper = np.array([
        pitch_max, yaw_max, roll_max,
        D_max,
        dfx_max, dfy_max,
        dcx_max, dcy_max
    ], dtype=np.float64)

    fixed = SystemFixed(Kp=Kp, Kc0=Kc0, dx=dx)

    # ---------- 优化 ----------
    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs),
        method="trf",
        bounds=(lower, upper),
        verbose=2
    )

    pitch_est, yaw_est, roll_est, D_est, d_fx_est, d_fy_est, d_cx_est, d_cy_est = result.x
    print("\nEstimated angles (deg):", pitch_est, yaw_est, roll_est)
    print("Estimated D (m):", D_est)

    fx_est = fx0 + d_fx_est
    fy_est = fy0 + d_fy_est
    cx_est = cx0 + d_cx_est
    cy_est = cy0 + d_cy_est

    Kc_est = np.array([[fx_est, 0.0, cx_est],
                       [0.0, fy_est, cy_est],
                       [0.0, 0.0, 1.0]], dtype=np.float64)

    print("Estimated camera intrinsics Kc:")
    print(Kc_est)

    # ---------- 用估计参数重新投影并计算误差 ----------
    Pw_est = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp, D_est)
    cam_pred_est = world_to_camera_pixel(Pw_est, Kc_est, dx, D_est,
                                         pitch_est, yaw_est, roll_est)

    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print("Mean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

    # ---------- 可视化：红色真值，绿色重投影 ----------
    for i, (x, y) in enumerate(cam_obs):
        cv2.circle(image, (int(x), int(y)), 5, (0, 0, 255), -1)   # obs: red
        x1, y1 = cam_pred_est[i]
        cv2.circle(image, (int(x1), int(y1)), 5, (0, 255, 0), -1) # pred: green

    cv2.imshow("Chessboard with Corners (red=obs, green=pred)", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()