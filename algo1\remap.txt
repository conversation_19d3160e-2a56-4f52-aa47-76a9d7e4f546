/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2010-2012, Institute Of Software Chinese Academy Of Science, all rights reserved.
// Copyright (C) 2010-2012, Advanced Micro Devices, Inc., all rights reserved.
// Third party copyrights are property of their respective owners.
//
// @Authors
//    <PERSON>, <EMAIL>
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors as is and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/

#ifdef DOUBLE_SUPPORT
#ifdef cl_amd_fp64
#pragma OPENCL EXTENSION cl_amd_fp64:enable
#elif defined (cl_khr_fp64)
#pragma OPENCL EXTENSION cl_khr_fp64:enable
#endif
#endif

#pragma OPENCL EXTENSION cl_khr_fp16 : enable

#define noconvert

#if cn != 3
#define loadpix(addr)  *(__global const T*)(addr)
#define storepix(val, addr)  *(__global T*)(addr) = val
#define TSIZE ((int)sizeof(T))
#define convertScalar(a) (a)
#else
#define loadpix(addr)  vload3(0, (__global const T1*)(addr))
#define storepix(val, addr) vstore3(val, 0, (__global T1*)(addr))
#define TSIZE ((int)sizeof(T1)*3)
#define convertScalar(a) (T)(a.x, a.y, a.z)
#endif

#define pi (3.141592653)
enum
{
    INTER_BITS = 5,
    INTER_TAB_SIZE = 1 << INTER_BITS,
    INTER_TAB_SIZE2 = INTER_TAB_SIZE * INTER_TAB_SIZE
};

#ifdef INTER_NEAREST
#define convertToWT
#endif

#ifdef BORDER_CONSTANT
#define EXTRAPOLATE(v2, v) v = scalar;
#elif defined BORDER_REPLICATE
#define EXTRAPOLATE(v2, v) \
    { \
        v2 = max(min(v2, (int2)(src_cols - 1, src_rows - 1)), (int2)(0)); \
        v = convertToWT(loadpix((__global const T*)(srcptr + mad24(v2.y, src_step, v2.x * TSIZE + src_offset)))); \
    }
#elif defined BORDER_WRAP
#define EXTRAPOLATE(v2, v) \
    { \
        if (v2.x < 0) \
            v2.x -= ((v2.x - src_cols + 1) / src_cols) * src_cols; \
        if (v2.x >= src_cols) \
            v2.x %= src_cols; \
        \
        if (v2.y < 0) \
            v2.y -= ((v2.y - src_rows + 1) / src_rows) * src_rows; \
        if( v2.y >= src_rows ) \
            v2.y %= src_rows; \
        v = convertToWT(loadpix((__global const T*)(srcptr + mad24(v2.y, src_step, v2.x * TSIZE + src_offset)))); \
    }
#elif defined(BORDER_REFLECT) || defined(BORDER_REFLECT_101)
#ifdef BORDER_REFLECT
#define DELTA int delta = 0
#else
#define DELTA int delta = 1
#endif
#define EXTRAPOLATE(v2, v) \
    { \
        DELTA; \
        if (src_cols == 1) \
            v2.x = 0; \
        else \
            do \
            { \
                if( v2.x < 0 ) \
                    v2.x = -v2.x - 1 + delta; \
                else \
                    v2.x = src_cols - 1 - (v2.x - src_cols) - delta; \
            } \
            while (v2.x >= src_cols || v2.x < 0); \
        \
        if (src_rows == 1) \
            v2.y = 0; \
        else \
            do \
            { \
                if( v2.y < 0 ) \
                    v2.y = -v2.y - 1 + delta; \
                else \
                    v2.y = src_rows - 1 - (v2.y - src_rows) - delta; \
            } \
            while (v2.y >= src_rows || v2.y < 0); \
        v = convertToWT(loadpix((__global const T*)(srcptr + mad24(v2.y, src_step, v2.x * TSIZE + src_offset)))); \
    }
#else
#error No extrapolation method
#endif

#define NEED_EXTRAPOLATION(gx, gy) (gx >= src_cols || gy >= src_rows || gx < 0 || gy < 0)

#ifdef INTER_NEAREST

__kernel void remap_2_32FC1(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            __global const uchar * map1ptr, int map1_step, int map1_offset,
                            __global const uchar * map2ptr, int map2_step, int map2_offset,
                            ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        T scalar = convertScalar(nVal);

        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(float), map1_offset));
        int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(float), map2_offset));
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map1_index += map1_step, map2_index += map2_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const float * map1 = (__global const float *)(map1ptr + map1_index);
                __global const float * map2 = (__global const float *)(map2ptr + map2_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                int gx = convert_int_sat_rte(map1[0]);
                int gy = convert_int_sat_rte(map2[0]);

                if (NEED_EXTRAPOLATION(gx, gy))
                {
#ifndef BORDER_CONSTANT
                    int2 gxy = (int2)(gx, gy);
#endif
                    T v;
                    EXTRAPOLATE(gxy, v)
                    storepix(v, dst);
                }
                else
                {
                    int src_index = mad24(gy, src_step, mad24(gx, TSIZE, src_offset));
                    storepix(loadpix((__global const T*)(srcptr + src_index)), dst);
                }
            }
    }
}

__kernel void remap_32FC2(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                          __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                          __global const uchar * mapptr, int map_step, int map_offset,
                          ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        T scalar = convertScalar(nVal);
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map_index = mad24(y, map_step, mad24(x, (int)sizeof(float2), map_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map_index += map_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const float2 * map = (__global const float2 *)(mapptr + map_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                int2 gxy = convert_int2_sat_rte(map[0]);
                int gx = gxy.x, gy = gxy.y;

                if (NEED_EXTRAPOLATION(gx, gy))
                {
                    T v;
                    EXTRAPOLATE(gxy, v)
                    storepix(v, dst);
                }
                else
                {
                    int src_index = mad24(gy, src_step, mad24(gx, TSIZE, src_offset));
                    storepix(loadpix((__global const T *)(srcptr + src_index)), dst);
                }
        }
    }
}

__kernel void remap_16SC2(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                          __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                          __global const uchar * mapptr, int map_step, int map_offset,
                          ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        T scalar = convertScalar(nVal);
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map_index = mad24(y, map_step, mad24(x, (int)sizeof(short2), map_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map_index += map_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const short2 * map = (__global const short2 *)(mapptr + map_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                int2 gxy = convert_int2(map[0]);
                int gx = gxy.x, gy = gxy.y;

                if (NEED_EXTRAPOLATION(gx, gy))
                {
                    T v;
                    EXTRAPOLATE(gxy, v)
                    storepix(v, dst);
                }
                else
                {
                    int src_index = mad24(gy, src_step, mad24(gx, TSIZE, src_offset));
                    storepix(loadpix((__global const T *)(srcptr + src_index)), dst);
                }
            }
    }
}

__kernel void remap_16SC2_16UC1(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                                __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                                __global const uchar * map1ptr, int map1_step, int map1_offset,
                                __global const uchar * map2ptr, int map2_step, int map2_offset,
                                ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        T scalar = convertScalar(nVal);
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(short2), map1_offset));
        int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(ushort), map2_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map1_index += map1_step, map2_index += map2_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const short2 * map1 = (__global const short2 *)(map1ptr + map1_index);
                __global const ushort * map2 = (__global const ushort *)(map2ptr + map2_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                int map2Value = convert_int(map2[0]) & (INTER_TAB_SIZE2 - 1);
                int dx = (map2Value & (INTER_TAB_SIZE - 1)) < (INTER_TAB_SIZE >> 1) ? 1 : 0;
                int dy = (map2Value >> INTER_BITS) < (INTER_TAB_SIZE >> 1) ? 1 : 0;
                int2 gxy = convert_int2(map1[0]) + (int2)(dx, dy);
                int gx = gxy.x, gy = gxy.y;

                if (NEED_EXTRAPOLATION(gx, gy))
                {
                    T v;
                    EXTRAPOLATE(gxy, v)
                    storepix(v, dst);
                }
                else
                {
                    int src_index = mad24(gy, src_step, mad24(gx, TSIZE, src_offset));
                    storepix(loadpix((__global const T *)(srcptr + src_index)), dst);
                }
            }
    }
}

#elif defined INTER_LINEAR

__constant float coeffs[64] =
{ 1.000000f, 0.000000f, 0.968750f, 0.031250f, 0.937500f, 0.062500f, 0.906250f, 0.093750f, 0.875000f, 0.125000f, 0.843750f, 0.156250f,
  0.812500f, 0.187500f, 0.781250f, 0.218750f, 0.750000f, 0.250000f, 0.718750f, 0.281250f, 0.687500f, 0.312500f, 0.656250f, 0.343750f,
  0.625000f, 0.375000f, 0.593750f, 0.406250f, 0.562500f, 0.437500f, 0.531250f, 0.468750f, 0.500000f, 0.500000f, 0.468750f, 0.531250f,
  0.437500f, 0.562500f, 0.406250f, 0.593750f, 0.375000f, 0.625000f, 0.343750f, 0.656250f, 0.312500f, 0.687500f, 0.281250f, 0.718750f,
  0.250000f, 0.750000f, 0.218750f, 0.781250f, 0.187500f, 0.812500f, 0.156250f, 0.843750f, 0.125000f, 0.875000f, 0.093750f, 0.906250f,
  0.062500f, 0.937500f, 0.031250f, 0.968750f };

__kernel void remap_16SC2_16UC1(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                                __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                                __global const uchar * map1ptr, int map1_step, int map1_offset,
                                __global const uchar * map2ptr, int map2_step, int map2_offset,
                                ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(short2), map1_offset));
        int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(ushort), map2_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map1_index += map1_step, map2_index += map2_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const short2 * map1 = (__global const short2 *)(map1ptr + map1_index);
                __global const ushort * map2 = (__global const ushort *)(map2ptr + map2_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                int2 map_dataA = convert_int2(map1[0]);
                int2 map_dataB = (int2)(map_dataA.x + 1, map_dataA.y);
                int2 map_dataC = (int2)(map_dataA.x, map_dataA.y + 1);
                int2 map_dataD = (int2)(map_dataA.x + 1, map_dataA.y + 1);

                ushort map2Value = (ushort)(map2[0] & (INTER_TAB_SIZE2 - 1));
                WT2 u = (WT2)(map2Value & (INTER_TAB_SIZE - 1), map2Value >> INTER_BITS) / (WT2)(INTER_TAB_SIZE);

                WT a = scalar, b = scalar, c = scalar, d = scalar;

                if (!NEED_EXTRAPOLATION(map_dataA.x, map_dataA.y))
                    a = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataA.y, src_step, map_dataA.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataA, a);

                if (!NEED_EXTRAPOLATION(map_dataB.x, map_dataB.y))
                    b = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataB.y, src_step, map_dataB.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataB, b);

                if (!NEED_EXTRAPOLATION(map_dataC.x, map_dataC.y))
                    c = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataC.y, src_step, map_dataC.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataC, c);

                if (!NEED_EXTRAPOLATION(map_dataD.x, map_dataD.y))
                    d = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataD.y, src_step, map_dataD.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataD, d);

                WT dst_data = a * (1 - u.x) * (1 - u.y) +
                              b * (u.x)     * (1 - u.y) +
                              c * (1 - u.x) * (u.y) +
                              d * (u.x)     * (u.y);
                storepix(convertToT(dst_data), dst);
            }
    }
}

#define sign(x)   ((x > 0) ? (1) : ((x < 0) ? (-1): 0))
#define is_neg(x)   (x < 0)

float4 sign_vec(float4 x)
{
	return (float4)((float)sign(x.x), (float)sign(x.y), (float)sign(x.z), (float)sign(x.w));
}
float4 is_neg_vec(float4 x)
{
	return (float4)((float)is_neg(x.x), (float)is_neg(x.y), (float)is_neg(x.z), (float)is_neg(x.w));
}

float4 acos_fast(float4 x) {
	float4 negate = is_neg_vec(x);
	x = fabs(x);
	float4 ret = (float4)(-0.0187293f);
	ret = ret * x;
	ret = ret + 0.0742610f;
	ret = ret * x;
	ret = ret - 0.2121144f;
	ret = ret * x;
	ret = ret + 1.5707288f;
	ret = ret * native_sqrt(1.f - x);
	ret = ret - 2.f * negate * ret;
	return negate * 3.14159265358979f + ret;
}

float acos_fast_float(float x) {
	float negate = is_neg(x);
	x = fabs(x);
	float ret = (-0.0187293f);
	ret = ret * x;
	ret = ret + 0.0742610f;
	ret = ret * x;
	ret = ret - 0.2121144f;
	ret = ret * x;
	ret = ret + 1.5707288f;
	ret = ret * native_sqrt(1.f - x);
	ret = ret - 2.f * negate * ret;
	return negate * 3.14159265358979f + ret;
}

__kernel void remap_2_32FC1_yuv_total_opt(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            ST nVal,
							__global const float * p, const float f, 
							const float mx0, const float mxstep,  
							const float my0, const float mystep,  
							float angle_x, float angle_y)
{
    int x = get_global_id(0);
    int y = get_global_id(1);

    if (x < dst_cols)
    {
		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
			
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mul24(x, TSIZE));
		int dst_indexv = 0;
		int caluv_flag = 0;
		if (((x & 0x1) == 0) && ((y & 0x1) == 0))
		{
			caluv_flag = 1;
		}
		
		if (caluv_flag)
		{
			dst_indexv = mad24(y >> 1, dst_step, mul24(x - (x & 0x1), TSIZE)) + dst_rows * dst_cols;
		}
			
        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            dst_index += dst_step, dst_indexv += dst_step)
            if (y < dst_rows)
            {
                float xc = native_divide((x * mxstep + mx0 -u0), mu);
				float yc = native_divide(y * mystep + my0 -v0, mv);
				
				float cos_phi = xc / native_sqrt(xc*xc+yc * yc);
				float sin_phi = yc / native_sqrt(xc*xc+yc * yc);
				float cos_theta = sign(xc)*f*cos_phi / native_sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
				float sin_theta = sign(xc)*xc / native_sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
			
				float vec_x = sin_theta * cos_phi;
				float vec_y = sin_theta * sin_phi;
				float vec_z = cos_theta;
				float ry = pi * 0.5 - angle_x;
				float rx = angle_y - pi * 0.5;
				float cos_ry = native_cos(ry);
				float sin_ry = native_sin(ry);
				float cos_rx = native_cos(rx);
				float sin_rx = native_sin(rx);
				float vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
				float vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
				float vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
				float theta_rotated = acos_fast_float(vec_rotated_z);
				float vec_rotated_norm = native_sqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
				float cos_phi_rotated = vec_rotated_x / vec_rotated_norm;
				float sin_phi_rotated = vec_rotated_y / vec_rotated_norm;

				float r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
				float xdst = r*cos_phi_rotated;
				float ydst = r*sin_phi_rotated;
				
                __global T * dst = (__global T *)(dstptr + dst_index);				

                float xf = xdst*mu+u0, yf = ydst*mv+v0;
				
                int sx = convert_int_sat_rtz(mad(xf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;
                int sy = convert_int_sat_rtz(mad(yf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;

                __constant float * coeffs_x = coeffs + ((convert_int_rte(xf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);
                __constant float * coeffs_y = coeffs + ((convert_int_rte(yf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);

                WT sum = (WT)(0), xsum;
				float2 sumvu = (float2)(0);
				float2 xsumvu;
                int src_index = mad24(sy, src_step, mul24(sx, TSIZE));
				int src_indexv = 0;
				if (caluv_flag)
				{
					src_indexv = mad24(sy / 2, src_step, mul24(sx - (sx & 0x1), TSIZE)) + src_rows * src_cols;
				}
				
                #pragma unroll
                for (int yp = 0; yp < 2; ++yp, src_index += src_step, src_indexv += src_step)
                {
                    if (sy + yp >= 0 && sy + yp < src_rows)
                    {
                        xsum = (WT)(0);
						xsumvu = (float2)(0);
                        if (sx >= 0 && sx + 2 < src_cols)
                        {
#if SRC_DEPTH == 0 && cn == 1
                            uchar2 value = vload2(0, srcptr + src_index);
                            xsum = dot(convert_float2(value), (float2)(coeffs_x[0], coeffs_x[1]));
							if (caluv_flag)
							{
								#pragma unroll
								for (int xp = 0; xp < 2; ++xp)
								{
									xsumvu = fma(convert_float2(vload2(0, srcptr + mul24(xp, 2) + src_indexv)), coeffs_x[xp], xsumvu);
								}								
							}

#else
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))), coeffs_x[xp], xsum);
#endif
                        }
                        else
                        {
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
							{
								xsum = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))) : scalar, coeffs_x[xp], xsum);
								if (caluv_flag)
								{
									xsumvu = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convert_float2(vload2(0, srcptr + mad24(xp * 2, TSIZE, src_indexv))) : (float2)(scalar), coeffs_x[xp], xsumvu);									
								}
							}
                                
                        }
                        sum = fma(xsum, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumvu = fma(xsumvu, coeffs_y[yp], sumvu);
						}
                    }
                    else
                        sum = fma(scalar, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumvu = fma((float2)(scalar), coeffs_y[yp], sumvu);
						}
                }

                storepix(convertToT(sum), dst);

				if (caluv_flag)
				{
					vstore2(convert_uchar2_sat_rte(sumvu), dst_indexv >> 1, dstptr);
				}
            }
    }
}

__kernel void remap_2_32FC1_yuv_total(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            ST nVal,
							__global const float * p, const float f, 
							const float mx0, const float mxstep,  
							const float my0, const float mystep,  
							float angle_x, float angle_y)
{
    int x = get_global_id(0);
    int y = get_global_id(1);

    if (x < dst_cols)
    {
		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
			
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mul24(x, TSIZE));
		int dst_indexv = 0;
		int caluv_flag = 0;
		if (((x & 0x1) == 0) && ((y & 0x1) == 0))
		{
			caluv_flag = 1;
		}
		
		if (caluv_flag)
		{
			dst_indexv = mad24(y >> 1, dst_step, mul24(x - (x & 0x1), TSIZE)) + dst_rows * dst_cols;
		}
			
        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            dst_index += dst_step, dst_indexv += dst_step)
            if (y < dst_rows)
            {
                float xc = native_divide((x * mxstep + mx0 -u0), mu);
				float yc = native_divide(y * mystep + my0 -v0, mv);
				
				float cos_phi = xc / sqrt(xc*xc+yc * yc);
				float sin_phi = yc / sqrt(xc*xc+yc * yc);
				float cos_theta = sign(xc)*f*cos_phi / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
				float sin_theta = sign(xc)*xc / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
			
				float vec_x = sin_theta * cos_phi;
				float vec_y = sin_theta * sin_phi;
				float vec_z = cos_theta;
				float ry = pi * 0.5 - angle_x;
				float rx = angle_y - pi * 0.5;
				float cos_ry = native_cos(ry);
				float sin_ry = native_sin(ry);
				float cos_rx = native_cos(rx);
				float sin_rx = native_sin(rx);
				float vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
				float vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
				float vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
				float theta_rotated = acos_fast_float(vec_rotated_z);
				float vec_rotated_norm = sqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
				float cos_phi_rotated = vec_rotated_x / vec_rotated_norm;
				float sin_phi_rotated = vec_rotated_y / vec_rotated_norm;

				float r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
				float xdst = r*cos_phi_rotated;
				float ydst = r*sin_phi_rotated;
				
                __global T * dst = (__global T *)(dstptr + dst_index);				

                float xf = xdst*mu+u0, yf = ydst*mv+v0;
				
                int sx = convert_int_sat_rtz(mad(xf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;
                int sy = convert_int_sat_rtz(mad(yf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;

                __constant float * coeffs_x = coeffs + ((convert_int_rte(xf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);
                __constant float * coeffs_y = coeffs + ((convert_int_rte(yf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);

                WT sum = (WT)(0), xsum, sumv = (WT)(0), sumu = (WT)(0), xsumv, xsumu;
                int src_index = mad24(sy, src_step, mul24(sx, TSIZE));
				int src_indexv = 0;
				int src_indexu = 0;
				if (caluv_flag)
				{
					src_indexv = mad24(sy / 2, src_step, mul24(sx - (sx & 0x1), TSIZE)) + src_rows * src_cols;
					src_indexu = src_indexv + 1;
				}
				
                #pragma unroll
                for (int yp = 0; yp < 2; ++yp, src_index += src_step, src_indexv += src_step, src_indexu += src_step)
                {
                    if (sy + yp >= 0 && sy + yp < src_rows)
                    {
                        xsum = (WT)(0);
                        if (sx >= 0 && sx + 2 < src_cols)
                        {
#if SRC_DEPTH == 0 && cn == 1
                            uchar2 value = vload2(0, srcptr + src_index);
                            xsum = dot(convert_float2(value), (float2)(coeffs_x[0], coeffs_x[1]));
							if (caluv_flag)
							{
								uchar2 valuev = (uchar2)(*(srcptr + src_indexv), *(srcptr + src_indexv + 2));
								xsumv = dot(convert_float2(valuev), (float2)(coeffs_x[0], coeffs_x[1]));
								uchar2 valueu = (uchar2)(*(srcptr + src_indexu), *(srcptr + src_indexu + 2));
								xsumu = dot(convert_float2(valueu), (float2)(coeffs_x[0], coeffs_x[1]));
							}

#else
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))), coeffs_x[xp], xsum);
#endif
                        }
                        else
                        {
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
							{
								xsum = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))) : scalar, coeffs_x[xp], xsum);
								if (caluv_flag)
								{
									xsumv = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp * 2, TSIZE, src_indexv))) : scalar, coeffs_x[xp], xsumv);
									xsumu = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp * 2, TSIZE, src_indexu))) : scalar, coeffs_x[xp], xsumu);
								}
							}
                                
                        }
                        sum = fma(xsum, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumv = fma(xsumv, coeffs_y[yp], sumv);
							sumu = fma(xsumu, coeffs_y[yp], sumu);
						}
                    }
                    else
                        sum = fma(scalar, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumv = fma(scalar, coeffs_y[yp], sumv);
							sumu = fma(scalar, coeffs_y[yp], sumu);
						}
                }

                storepix(convertToT(sum), dst);

				if (caluv_flag)
				{
					vstore2((uchar2)(convertToT(sumv), convertToT(sumu)), dst_indexv >> 1, dstptr);
				}
            }
    }
}

__kernel void calculate_map_total_vec(__global const float * p, const float f, 
							const float mx0, const float mxstep,  
							const float my0, const float mystep,  
							float angle_x, float angle_y,
                            __global float * map1ptr,  __global float * map2ptr, 
							int step, int rows, int cols)
{
	int x = get_global_id(0);
    int y = get_global_id(1);
	if (x < cols / 4 && y < rows)
	{	
		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
		
		int index = y * step + x * 4;
		float4 xcoor = (float4)((float)(x * 4), (float)(x * 4 + 1), (float)(x * 4 + 2), (float)(x * 4 + 3));		
		float4 xc = xcoor * mxstep + mx0;
		float4 yc = (float4)(y * mystep + my0);
		
		//float4 cos_phi = native_divide(xc, native_sqrt(xc*xc+yc * yc));
		//float4 sin_phi = native_divide(yc, native_sqrt(xc*xc+yc * yc));
		//float4 cos_theta = native_divide(sign_vec(xc)*f*cos_phi, native_sqrt(xc*xc+(f*cos_phi)*(f*cos_phi)));
		//float4 sin_theta = native_divide(sign_vec(xc)*xc, native_sqrt(xc*xc+(f*cos_phi)*(f*cos_phi)));
		
		float4 cos_phi = xc * native_rsqrt(xc*xc+yc * yc);
		float4 sin_phi = yc * native_rsqrt(xc*xc+yc * yc );
		float4 cos_theta = sign_vec(xc)*f*cos_phi * native_rsqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
		float4 sin_theta = sign_vec(xc)*xc * native_rsqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
	
		float4 vec_x = sin_theta * cos_phi;
		float4 vec_y = sin_theta * sin_phi;
		float4 vec_z = cos_theta;
		//float4 ry = pi * 0.5 - angle_x;
		//float4 rx = angle_y - pi * 0.5;
		float4 cos_ry = native_cos(angle_y);
		float4 sin_ry = native_sin(angle_y);
		float4 cos_rx = native_cos(angle_x);
		float4 sin_rx = native_sin(angle_x);
		float4 vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
		float4 vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
		float4 vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
		float4 theta_rotated = acos_fast(vec_rotated_z);
		float4 vec_rotated_norm = native_rsqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
		float4 cos_phi_rotated = vec_rotated_x * vec_rotated_norm;
		float4 sin_phi_rotated = vec_rotated_y * vec_rotated_norm;

		float4 r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
		float4 xdst = r*cos_phi_rotated;
		float4 ydst = r*sin_phi_rotated;
		
		vstore4(xdst*mu+u0, index >> 2, map1ptr);		
		vstore4(ydst*mv+v0, index >> 2, map2ptr);
	}
}

#define INC(x,l) min(x+1,l-1)

__kernel void get_global_map_roi(__global const float * gmap1ptr, __global const float * gmap2ptr,
								int gstep, int grows, int gcols, 
								__global float * map1ptr, __global float * map2ptr,
								int step, int rows, int cols, int roix, int roiy, int roiw, int roih)
{
	int dx = get_global_id(0);
    int dy = get_global_id(1);
	if (dx < cols && dy < rows)
	{
		float ifx = (float)roiw / cols;
		float ify = (float)roih / rows;
		float sx = ((dx+0.5f) * ifx - 0.5f), sy = ((dy+0.5f) * ify - 0.5f);
		int x = floor(sx), y = floor(sy);
		
		float u = sx - x, v = sy - y;
		if ( x<0 ) x=0,u=0;
        if ( x>=gcols ) x=gcols-1,u=0;
        if ( y<0 ) y=0,v=0;
        if ( y>=grows ) y=grows-1,v=0;
		
		int y_ = INC(y, grows);
        int x_ = INC(x, gcols);
		
		float u1 = 1.f - u;
        float v1 = 1.f - v;
        float map1data0 = *(gmap1ptr + mad24(y + roiy, gstep, x + roix));
        float map1data1 = *(gmap1ptr + mad24(y + roiy, gstep, x_ + roix));
        float map1data2 = *(gmap1ptr + mad24(y_+ roiy, gstep, x + roix));
        float map1data3 = *(gmap1ptr + mad24(y_+ roiy, gstep, x_ + roix));
		
		float map2data0 = *(gmap2ptr + mad24(y + roiy, gstep, x + roix));
        float map2data1 = *(gmap2ptr + mad24(y + roiy, gstep, x_ + roix));
        float map2data2 = *(gmap2ptr + mad24(y_ + roiy, gstep, x + roix));
        float map2data3 = *(gmap2ptr + mad24(y_ + roiy, gstep, x_ + roix));

        float uval1 = ((u1 * v1) * map1data0 + (u * v1) * map1data1 + (u1 * v) * map1data2 + (u * v) * map1data3);
		float uval2 = ((u1 * v1) * map2data0 + (u * v1) * map2data1 + (u1 * v) * map2data2 + (u * v) * map2data3);
 
		*(map1ptr + mad24(dy, step, dx)) = uval1;	
		*(map2ptr + mad24(dy, step, dx)) = uval2;			
	}
}

__kernel void interpolate_map(__global const float * map1ptrx, __global const float * map1ptry,
							  __global const float * map2ptrx, __global const float * map2ptry,
								int step, int rows, int cols, float w,
								__global float * mapptrx, __global float * mapptry)
{
	int dx = get_global_id(0);
    int dy = get_global_id(1);
	if (dx < cols && dy < rows)
	{	
		float w1 = 1.f - w;

        float map1x = *(map1ptrx + mad24(dy, step, dx));
        float map1y = *(map1ptry + mad24(dy, step, dx));
        float map2x = *(map2ptrx + mad24(dy, step, dx));
        float map2y = *(map2ptry + mad24(dy, step, dx));
		
        float uval1 = w * map1x + w1 * map2x;
		float uval2 = w * map1y + w1 * map2y;
 
		*(mapptrx + mad24(dy, step, dx)) = uval1;	
		*(mapptry + mad24(dy, step, dx)) = uval2;			
	}
}

__kernel void copy_map(__global const float * srcmap1ptr, __global const float * srcmap2ptr,
								int step, int rows, int cols,
								__global float * dstmap1ptr, __global float * dstmap2ptr)
{
	int dx = get_global_id(0);
    int dy = get_global_id(1);
	if (dx < cols && dy < rows)
	{	
        float uval1 = *(srcmap1ptr + mad24(dy, step, dx));
        float uval2 = *(srcmap2ptr + mad24(dy, step, dx));

		*(dstmap1ptr + mad24(dy, step, dx)) = uval1;	
		*(dstmap2ptr + mad24(dy, step, dx)) = uval2;			
	}
}


__kernel void calculate_map_total(__global const float * p, const float f, 
							const float mx0, const float mxstep,  
							const float my0, const float mystep,  
							float angle_x, float angle_y,
                            __global float * map1ptr,  __global float * map2ptr, 
							int step, int rows, int cols)
{
	int x = get_global_id(0);
    int y = get_global_id(1);
	if (x < cols && y < rows)
	{	
		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
		
		int index = y * step + x;
		
		float xc = (x * mxstep + mx0 -u0)/mu;
		float yc = (y * mystep + my0 -v0)/mv;
		
		float cos_phi = xc / sqrt(xc*xc+yc*yc);
		float sin_phi = yc / sqrt(xc*xc+yc*yc);
		float cos_theta = sign(xc)*f*cos_phi / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
		float sin_theta = sign(xc)*xc / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi));
	
		float vec_x = sin_theta * cos_phi;
		float vec_y = sin_theta * sin_phi;
		float vec_z = cos_theta;
		float ry = pi * 0.5 - angle_x;
		float rx = angle_y - pi * 0.5;
		float cos_ry = cos(ry);
		float sin_ry = sin(ry);
		float cos_rx = cos(rx);
		float sin_rx = sin(rx);
		float vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
		float vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
		float vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
		float theta_rotated = acos(vec_rotated_z);
		float vec_rotated_norm = sqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
		float cos_phi_rotated = vec_rotated_x / vec_rotated_norm;
		float sin_phi_rotated = vec_rotated_y / vec_rotated_norm;

		float r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
		float xdst = r*cos_phi_rotated;
		float ydst = r*sin_phi_rotated;
		
		*(map1ptr + index) = xdst*mu+u0;
		*(map2ptr + index) = ydst*mv+v0;
	}
}

__kernel void calculate_map_total_v1(__global const float * p, const float f, 
							const float mx0, const float mxstep,  
							const float my0, const float mystep,  
							float angle_x, float angle_y,
                            __global float * map1ptr,  __global float * map2ptr, 
							int step, int rows, int cols)
{
	int x = get_global_id(0);
    int y = get_global_id(1);
	if (x < cols && y < rows)
	{	
		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
		
		int index = y * step + x;
		
		float xc = (x * mxstep + mx0 -u0)/mu;
		float yc = (y * mystep + my0 -v0)/mv;
		
		float cos_phi = xc / sqrt(xc*xc+yc*yc + 0.00001f);
		float sin_phi = yc / sqrt(xc*xc+yc*yc + 0.00001f);
		float cos_theta = sign(xc)*f*cos_phi / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi) + 0.00001f);
		float sin_theta = sign(xc)*xc / sqrt(xc*xc+(f*cos_phi)*(f*cos_phi) + 0.00001f);
	
		float vec_x = sin_theta * cos_phi;
		float vec_y = sin_theta * sin_phi;
		float vec_z = cos_theta;
		float ry = pi * 0.5 - angle_x;
		float rx = angle_y - pi * 0.5;
		float cos_ry = cos(ry);
		float sin_ry = sin(ry);
		float cos_rx = cos(rx);
		float sin_rx = sin(rx);
		float vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
		float vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
		float vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
		float theta_rotated = acos(vec_rotated_z);
		float vec_rotated_norm = sqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
		float cos_phi_rotated = vec_rotated_x / vec_rotated_norm;
		float sin_phi_rotated = vec_rotated_y / vec_rotated_norm;

		float r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
		float xdst = r*cos_phi_rotated;
		float ydst = r*sin_phi_rotated;
		
		*(map1ptr + index) = xdst*mu+u0;
		*(map2ptr + index) = ydst*mv+v0;
	}
}

__kernel void calculate_map(__global const float * cos_phi, __global const float * sin_phi,
							__global const float * cos_theta, __global const float * sin_theta,
							__global const float * p,
							float angle_x, float angle_y,
                            __global float * map1ptr,  __global float * map2ptr, int step, int rows, int cols)
{
	int x = get_global_id(0);
    int y = get_global_id(1);
	if (x < cols && y < rows)
	{
		int index = y * step + x;
		float vec_x = (*(sin_theta + index)) * (*(cos_phi + index));
		float vec_y = (*(sin_theta + index)) * (*(sin_phi + index));
		float vec_z = *(cos_theta + index);
		float ry = pi * 0.5 - angle_x;
		float rx = angle_y - pi * 0.5;
		float cos_ry = cos(ry);
		float sin_ry = sin(ry);
		float cos_rx = cos(rx);
		float sin_rx = sin(rx);
		float vec_rotated_x = cos_ry*vec_x+sin_rx*sin_ry*vec_y + cos_rx*sin_ry*vec_z;
		float vec_rotated_y = cos_rx*vec_y-sin_rx*vec_z;
		float vec_rotated_z = -sin_ry*vec_x+sin_rx*cos_ry*vec_y+cos_rx*cos_ry*vec_z;
		float theta_rotated = acos(vec_rotated_z);
		float vec_rotated_norm = sqrt(vec_rotated_x*vec_rotated_x+vec_rotated_y*vec_rotated_y);
		float cos_phi_rotated = vec_rotated_x / vec_rotated_norm;
		float sin_phi_rotated = vec_rotated_y / vec_rotated_norm;

		float k1 = *p;
		float k2 = *(p + 1);
		float mu = *(p + 2);
		float mv = *(p + 3);
		float u0 = *(p + 4);
		float v0 = *(p + 5);
		float k3 = *(p + 6);
		float k4 = *(p + 7);
		float k5 = *(p + 8);
		float r = k5*pown(theta_rotated, 9) + k4*pown(theta_rotated, 7) + k3*pown(theta_rotated, 5) + k2*pown(theta_rotated, 3) + k1*theta_rotated;
		float x = r*cos_phi_rotated;
		float y = r*sin_phi_rotated;
		
		*(map1ptr + index) = x*mu+u0;
		*(map2ptr + index) = y*mv+v0;
	}
}

__kernel void remap_indentity(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            __global const uchar * map1ptr, int map1_step, int map1_offset,
                            __global const uchar * map2ptr, int map2_step, int map2_offset,
                            ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        int dst_index = mad24(y, dst_step, mad24(x, ((int)sizeof(T1)), dst_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y, dst_index += dst_step)
            if (y < dst_rows)
            {
				int src_index = mad24(y, src_step, mad24(x, ((int)sizeof(T1)), src_offset));
                __global T1 * dst = (__global T1 *)(dstptr + dst_index);

				*dst = *(srcptr + src_index);
				
				if (x % 2 == 0 && y % 2 == 0)
				{
					int dst_indexvu = mad24(y / 2, dst_step / 2, mad24(x / 2, ((int)sizeof(T1)), dst_offset));
					__global T1 * dstvu = (__global T1 *)(dstptr + dst_indexvu * 2 + dst_rows * dst_cols);
					int src_indexvu = mad24(y / 2, src_step / 2, mad24(x / 2, ((int)sizeof(T1)), src_offset));
					*dstvu = *(srcptr + src_indexvu * 2 + src_rows * src_cols);
					*(dstvu + 1) = *(srcptr + src_indexvu * 2 + src_rows * src_cols + 1);
				}
            }
    }
}

__kernel void test_kernel(__global const float * map1ptr, __global const uchar * map2ptr)
{
    int x = get_global_id(0);
    int y = get_global_id(1);
}

__kernel void remap_2_32FC1_yuv(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            __global const uchar * map1ptr, int map1_step, int map1_offset,
                            __global const uchar * map2ptr, int map2_step, int map2_offset,
                            ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1);

    if (x < dst_cols)
    {
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mul24(x, TSIZE));
        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(float), 0));
        //int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(float), 0));
		int dst_indexv = 0;
		//int dst_indexu = 0;
		int caluv_flag = 0;
		if (((x & 0x1) == 0) && ((y & 0x1) == 0))
		{
			caluv_flag = 1;
		}
		
		if (caluv_flag)
		{
			dst_indexv = mad24(y / 2, dst_step, mul24(x - (x & 0x1), TSIZE)) + dst_rows * dst_cols;
			//dst_indexu = dst_indexv + 1;
		}
			
        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            dst_index += dst_step, dst_indexv += dst_step)
            if (y < dst_rows)
            {
                __global const float * map1 = (__global const float *)(map1ptr + map1_index);
                __global const float * map2 = (__global const float *)(map2ptr + map1_index);
                __global T * dst = (__global T *)(dstptr + dst_index);				

#if defined BORDER_CONSTANT
                float xf = map1[0], yf = map2[0];
                
                int sx = convert_int_sat_rtz(mad(xf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;
                int sy = convert_int_sat_rtz(mad(yf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;

                __constant float * coeffs_x = coeffs + ((convert_int_rte(xf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);
                __constant float * coeffs_y = coeffs + ((convert_int_rte(yf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);

                WT sum = (WT)(0), xsum, sumv = (WT)(0), sumu = (WT)(0), xsumv, xsumu;
                int src_index = mad24(sy, src_step, mul24(sx, TSIZE));
				int src_indexv = 0;
				int src_indexu = 0;
				if (caluv_flag)
				{
					src_indexv = mad24(sy / 2, src_step, mul24(sx - (sx & 0x1), TSIZE)) + src_rows * src_cols;
					src_indexu = src_indexv + 1;
				}
				
                #pragma unroll
                for (int yp = 0; yp < 2; ++yp, src_index += src_step, src_indexv += src_step, src_indexu += src_step)
                {
                    if (sy + yp >= 0 && sy + yp < src_rows)
                    {
                        xsum = (WT)(0);
                        if (sx >= 0 && sx + 2 < src_cols)
                        {
#if SRC_DEPTH == 0 && cn == 1
                            uchar2 value = vload2(0, srcptr + src_index);
                            xsum = dot(convert_float2(value), (float2)(coeffs_x[0], coeffs_x[1]));
							if (caluv_flag)
							{
								uchar2 valuev = (uchar2)(*(srcptr + src_indexv), *(srcptr + src_indexv + 2));
								xsumv = dot(convert_float2(valuev), (float2)(coeffs_x[0], coeffs_x[1]));
								uchar2 valueu = (uchar2)(*(srcptr + src_indexu), *(srcptr + src_indexu + 2));
								xsumu = dot(convert_float2(valueu), (float2)(coeffs_x[0], coeffs_x[1]));
							}

#else
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))), coeffs_x[xp], xsum);
#endif
                        }
                        else
                        {
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
							{
								xsum = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))) : scalar, coeffs_x[xp], xsum);
								if (caluv_flag)
								{
									xsumv = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp * 2, TSIZE, src_indexv))) : scalar, coeffs_x[xp], xsumv);
									xsumu = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp * 2, TSIZE, src_indexu))) : scalar, coeffs_x[xp], xsumu);
								}
							}
                                
                        }
                        sum = fma(xsum, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumv = fma(xsumv, coeffs_y[yp], sumv);
							sumu = fma(xsumu, coeffs_y[yp], sumu);
						}

                    }
                    else
                        sum = fma(scalar, coeffs_y[yp], sum);
						if (caluv_flag)
						{
							sumv = fma(scalar, coeffs_y[yp], sumv);
							sumu = fma(scalar, coeffs_y[yp], sumu);
						}
                }

                storepix(convertToT(sum), dst);

				if (caluv_flag)
				{
					__global T * dstv = (__global T *)(dstptr + dst_indexv);
					__global T * dstu = (__global T *)(dstptr + dst_indexv + 1);
					storepix(convertToT(sumv), dstv);
					storepix(convertToT(sumu), dstu);
				}

#else
                float2 map_data = (float2)(map1[0], map2[0]);

                int2 map_dataA = convert_int2_sat_rtn(map_data);
                int2 map_dataB = (int2)(map_dataA.x + 1, map_dataA.y);
                int2 map_dataC = (int2)(map_dataA.x, map_dataA.y + 1);
                int2 map_dataD = (int2)(map_dataA.x + 1, map_dataA.y + 1);

                float2 _u = map_data - convert_float2(map_dataA);
                WT2 u = convertToWT2(convert_int2_rte(convertToWT2(_u) * (WT2)INTER_TAB_SIZE)) / (WT2)INTER_TAB_SIZE;
                WT scalar = convertToWT(convertScalar(nVal));
                WT a = scalar, b = scalar, c = scalar, d = scalar;

                if (!NEED_EXTRAPOLATION(map_dataA.x, map_dataA.y))
                    a = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataA.y, src_step, map_dataA.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataA, a);

                if (!NEED_EXTRAPOLATION(map_dataB.x, map_dataB.y))
                    b = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataB.y, src_step, map_dataB.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataB, b);

                if (!NEED_EXTRAPOLATION(map_dataC.x, map_dataC.y))
                    c = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataC.y, src_step, map_dataC.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataC, c);

                if (!NEED_EXTRAPOLATION(map_dataD.x, map_dataD.y))
                    d = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataD.y, src_step, map_dataD.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataD, d);

                WT dst_data = a * (1 - u.x) * (1 - u.y) +
                              b * (u.x)     * (1 - u.y) +
                              c * (1 - u.x) * (u.y) +
                              d * (u.x)     * (u.y);
                storepix(convertToT(dst_data), dst);
#endif
            }
    }
}

__kernel void remap_2_32FC1(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            __global const uchar * map1ptr, int map1_step, int map1_offset,
                            __global const uchar * map2ptr, int map2_step, int map2_offset,
                            ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(float), map1_offset));
        int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(float), map2_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map1_index += map1_step, map2_index += map2_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const float * map1 = (__global const float *)(map1ptr + map1_index);
                __global const float * map2 = (__global const float *)(map2ptr + map2_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

#if defined BORDER_CONSTANT
                float xf = map1[0], yf = map2[0];
                int sx = convert_int_sat_rtz(mad(xf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;
                int sy = convert_int_sat_rtz(mad(yf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;

                __constant float * coeffs_x = coeffs + ((convert_int_rte(xf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);
                __constant float * coeffs_y = coeffs + ((convert_int_rte(yf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);

                WT sum = (WT)(0), xsum;
                int src_index = mad24(sy, src_step, mad24(sx, TSIZE, src_offset));

                #pragma unroll
                for (int yp = 0; yp < 2; ++yp, src_index += src_step)
                {
                    if (sy + yp >= 0 && sy + yp < src_rows)
                    {
                        xsum = (WT)(0);
                        if (sx >= 0 && sx + 2 < src_cols)
                        {
#if SRC_DEPTH == 0 && cn == 1
                            uchar2 value = vload2(0, srcptr + src_index);
                            xsum = dot(convert_float2(value), (float2)(coeffs_x[0], coeffs_x[1]));
#else
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))), coeffs_x[xp], xsum);
#endif
                        }
                        else
                        {
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))) : scalar, coeffs_x[xp], xsum);
                        }
                        sum = fma(xsum, coeffs_y[yp], sum);
                    }
                    else
                        sum = fma(scalar, coeffs_y[yp], sum);
                }

                storepix(convertToT(sum), dst);
#else
                float2 map_data = (float2)(map1[0], map2[0]);

                int2 map_dataA = convert_int2_sat_rtn(map_data);
                int2 map_dataB = (int2)(map_dataA.x + 1, map_dataA.y);
                int2 map_dataC = (int2)(map_dataA.x, map_dataA.y + 1);
                int2 map_dataD = (int2)(map_dataA.x + 1, map_dataA.y + 1);

                float2 _u = map_data - convert_float2(map_dataA);
                WT2 u = convertToWT2(convert_int2_rte(convertToWT2(_u) * (WT2)INTER_TAB_SIZE)) / (WT2)INTER_TAB_SIZE;
                WT scalar = convertToWT(convertScalar(nVal));
                WT a = scalar, b = scalar, c = scalar, d = scalar;

                if (!NEED_EXTRAPOLATION(map_dataA.x, map_dataA.y))
                    a = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataA.y, src_step, map_dataA.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataA, a);

                if (!NEED_EXTRAPOLATION(map_dataB.x, map_dataB.y))
                    b = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataB.y, src_step, map_dataB.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataB, b);

                if (!NEED_EXTRAPOLATION(map_dataC.x, map_dataC.y))
                    c = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataC.y, src_step, map_dataC.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataC, c);

                if (!NEED_EXTRAPOLATION(map_dataD.x, map_dataD.y))
                    d = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataD.y, src_step, map_dataD.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataD, d);

                WT dst_data = a * (1 - u.x) * (1 - u.y) +
                              b * (u.x)     * (1 - u.y) +
                              c * (1 - u.x) * (u.y) +
                              d * (u.x)     * (u.y);
                storepix(convertToT(dst_data), dst);
#endif
            }
    }
}


__kernel void remap_2_32FC1_tmp(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                            __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                            __global const uchar * map1ptr, int map1_step, int map1_offset,
                            __global const uchar * map2ptr, int map2_step, int map2_offset,
                            ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map1_index = mad24(y, map1_step, mad24(x, (int)sizeof(float), map1_offset));
        int map2_index = mad24(y, map2_step, mad24(x, (int)sizeof(float), map2_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map1_index += map1_step, map2_index += map2_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const float * map1 = (__global const float *)(map1ptr + map1_index);
                __global const float * map2 = (__global const float *)(map2ptr + map2_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

#if defined BORDER_CONSTANT
                float xf = map1[0], yf = map2[0];
                int sx = convert_int_sat_rtz(mad(xf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;
                int sy = convert_int_sat_rtz(mad(yf, (float)INTER_TAB_SIZE, 0.5f)) >> INTER_BITS;

                __constant float * coeffs_x = coeffs + ((convert_int_rte(xf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);
                __constant float * coeffs_y = coeffs + ((convert_int_rte(yf * INTER_TAB_SIZE) & (INTER_TAB_SIZE - 1)) << 1);

                WT sum = (WT)(0), xsum;
                int src_index = mad24(sy, src_step, mad24(sx, TSIZE, src_offset));

                #pragma unroll
                for (int yp = 0; yp < 2; ++yp, src_index += src_step)
                {
                    if (sy + yp >= 0 && sy + yp < src_rows)
                    {
                        xsum = (WT)(0);
                        if (sx >= 0 && sx + 2 < src_cols)
                        {
#if SRC_DEPTH == 0 && cn == 1
                            uchar2 value = vload2(0, srcptr + src_index);
                            xsum = dot(convert_float2(value), (float2)(coeffs_x[0], coeffs_x[1]));
#else
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))), coeffs_x[xp], xsum);
#endif
                        }
                        else
                        {
                            #pragma unroll
                            for (int xp = 0; xp < 2; ++xp)
                                xsum = fma(sx + xp >= 0 && sx + xp < src_cols ?
                                           convertToWT(loadpix(srcptr + mad24(xp, TSIZE, src_index))) : scalar, coeffs_x[xp], xsum);
                        }
                        sum = fma(xsum, coeffs_y[yp], sum);
                    }
                    else
                        sum = fma(scalar, coeffs_y[yp], sum);
                }

                storepix(convertToT(sum), dst);
#else
                float2 map_data = (float2)(map1[0], map2[0]);

                int2 map_dataA = convert_int2_sat_rtn(map_data);
                int2 map_dataB = (int2)(map_dataA.x + 1, map_dataA.y);
                int2 map_dataC = (int2)(map_dataA.x, map_dataA.y + 1);
                int2 map_dataD = (int2)(map_dataA.x + 1, map_dataA.y + 1);

                float2 _u = map_data - convert_float2(map_dataA);
                WT2 u = convertToWT2(convert_int2_rte(convertToWT2(_u) * (WT2)INTER_TAB_SIZE)) / (WT2)INTER_TAB_SIZE;
                WT scalar = convertToWT(convertScalar(nVal));
                WT a = scalar, b = scalar, c = scalar, d = scalar;

                if (!NEED_EXTRAPOLATION(map_dataA.x, map_dataA.y))
                    a = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataA.y, src_step, map_dataA.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataA, a);

                if (!NEED_EXTRAPOLATION(map_dataB.x, map_dataB.y))
                    b = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataB.y, src_step, map_dataB.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataB, b);

                if (!NEED_EXTRAPOLATION(map_dataC.x, map_dataC.y))
                    c = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataC.y, src_step, map_dataC.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataC, c);

                if (!NEED_EXTRAPOLATION(map_dataD.x, map_dataD.y))
                    d = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataD.y, src_step, map_dataD.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataD, d);

                WT dst_data = a * (1 - u.x) * (1 - u.y) +
                              b * (u.x)     * (1 - u.y) +
                              c * (1 - u.x) * (u.y) +
                              d * (u.x)     * (u.y);
                storepix(convertToT(dst_data), dst);
#endif
            }
    }
}

__kernel void remap_32FC2(__global const uchar * srcptr, int src_step, int src_offset, int src_rows, int src_cols,
                          __global uchar * dstptr, int dst_step, int dst_offset, int dst_rows, int dst_cols,
                          __global const uchar * mapptr, int map_step, int map_offset,
                          ST nVal)
{
    int x = get_global_id(0);
    int y = get_global_id(1) * rowsPerWI;

    if (x < dst_cols)
    {
        WT scalar = convertToWT(convertScalar(nVal));
        int dst_index = mad24(y, dst_step, mad24(x, TSIZE, dst_offset));
        int map_index = mad24(y, map_step, mad24(x, (int)sizeof(float2), map_offset));

        #pragma unroll
        for (int i = 0; i < rowsPerWI; ++i, ++y,
            map_index += map_step, dst_index += dst_step)
            if (y < dst_rows)
            {
                __global const float2 * map = (__global const float2 *)(mapptr + map_index);
                __global T * dst = (__global T *)(dstptr + dst_index);

                float2 map_data = map[0];
                int2 map_dataA = convert_int2_sat_rtn(map_data);
                int2 map_dataB = (int2)(map_dataA.x + 1, map_dataA.y);
                int2 map_dataC = (int2)(map_dataA.x, map_dataA.y + 1);
                int2 map_dataD = (int2)(map_dataA.x + 1, map_dataA.y + 1);

                float2 _u = map_data - convert_float2(map_dataA);
                WT2 u = convertToWT2(convert_int2_rte(convertToWT2(_u) * (WT2)INTER_TAB_SIZE)) / (WT2)INTER_TAB_SIZE;
                WT a = scalar, b = scalar, c = scalar, d = scalar;

                if (!NEED_EXTRAPOLATION(map_dataA.x, map_dataA.y))
                    a = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataA.y, src_step, map_dataA.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataA, a);

                if (!NEED_EXTRAPOLATION(map_dataB.x, map_dataB.y))
                    b = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataB.y, src_step, map_dataB.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataB, b);

                if (!NEED_EXTRAPOLATION(map_dataC.x, map_dataC.y))
                    c = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataC.y, src_step, map_dataC.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataC, c);

                if (!NEED_EXTRAPOLATION(map_dataD.x, map_dataD.y))
                    d = convertToWT(loadpix((__global const T *)(srcptr + mad24(map_dataD.y, src_step, map_dataD.x * TSIZE + src_offset))));
                else
                    EXTRAPOLATE(map_dataD, d);

                WT dst_data = a * (1 - u.x) * (1 - u.y) +
                              b * (u.x)     * (1 - u.y) +
                              c * (1 - u.x) * (u.y) +
                              d * (u.x)     * (u.y);
                storepix(convertToT(dst_data), dst);
            }
    }
}

#endif
