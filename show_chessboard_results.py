#!/usr/bin/env python3
"""
显示棋盘格角点检测结果
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
import os

def show_detection_results():
    """显示检测结果"""
    # 读取角点坐标
    corners_file = "chessboard_corners_advanced.txt"
    if not os.path.exists(corners_file):
        print(f"角点文件不存在: {corners_file}")
        return
    
    # 读取角点坐标
    corners = np.loadtxt(corners_file, skiprows=2)
    print(f"成功读取 {len(corners)} 个角点坐标")
    
    # 显示角点坐标
    print("\n检测到的棋盘格角点坐标:")
    print("=" * 40)
    for i, (x, y) in enumerate(corners):
        print(f"角点 {i+1:2d}: ({x:7.2f}, {y:7.2f})")
    
    # 计算角点统计信息
    x_coords = corners[:, 0]
    y_coords = corners[:, 1]
    
    print(f"\n角点统计信息:")
    print("=" * 40)
    print(f"X坐标范围: {x_coords.min():.2f} - {x_coords.max():.2f}")
    print(f"Y坐标范围: {y_coords.min():.2f} - {y_coords.max():.2f}")
    print(f"X坐标平均值: {x_coords.mean():.2f} ± {x_coords.std():.2f}")
    print(f"Y坐标平均值: {y_coords.mean():.2f} ± {y_coords.std():.2f}")
    
    # 分析棋盘格结构 (5x4 模式)
    print(f"\n棋盘格结构分析 (5x4 模式):")
    print("=" * 40)
    
    # 重新排列角点为5x4网格
    corners_grid = corners.reshape(4, 5, 2)
    
    # 计算行间距和列间距
    row_spacings = []
    col_spacings = []
    
    for row in range(4):
        for col in range(4):  # 5列，4个间距
            dx = corners_grid[row, col+1, 0] - corners_grid[row, col, 0]
            col_spacings.append(dx)
    
    for row in range(3):  # 4行，3个间距
        for col in range(5):
            dy = corners_grid[row+1, col, 1] - corners_grid[row, col, 1]
            row_spacings.append(dy)
    
    print(f"列间距 (水平): {np.mean(col_spacings):.2f} ± {np.std(col_spacings):.2f} 像素")
    print(f"行间距 (垂直): {np.mean(row_spacings):.2f} ± {np.std(row_spacings):.2f} 像素")
    
    # 显示检测结果图像
    result_images = [
        "chessboard_detection_results/chessboard_blurred_5x4.jpg",
        "chessboard_detection_results/chessboard_equalized_5x4.jpg"
    ]
    
    for img_path in result_images:
        if os.path.exists(img_path):
            img = cv2.imread(img_path)
            if img is not None:
                # 缩放显示
                height, width = img.shape[:2]
                if width > 1200 or height > 800:
                    scale = min(1200/width, 800/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    img_resized = cv2.resize(img, (new_width, new_height))
                    
                    window_name = f"Detection Result - {os.path.basename(img_path)}"
                    cv2.imshow(window_name, img_resized)
                    print(f"显示: {img_path} (缩放比例: {scale:.2f})")
                else:
                    window_name = f"Detection Result - {os.path.basename(img_path)}"
                    cv2.imshow(window_name, img)
                    print(f"显示: {img_path}")
    
    print("\n按任意键关闭所有窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def create_corner_analysis_plot():
    """创建角点分析图表"""
    corners_file = "chessboard_corners_advanced.txt"
    if not os.path.exists(corners_file):
        print(f"角点文件不存在: {corners_file}")
        return
    
    corners = np.loadtxt(corners_file, skiprows=2)
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 角点分布图
    ax1.scatter(corners[:, 0], corners[:, 1], c='red', s=50, alpha=0.7)
    ax1.set_title('棋盘格角点分布', fontsize=14)
    ax1.set_xlabel('X 坐标 (像素)')
    ax1.set_ylabel('Y 坐标 (像素)')
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()  # 图像坐标系
    
    # 添加角点编号
    for i, (x, y) in enumerate(corners):
        ax1.annotate(f'{i+1}', (x, y), xytext=(5, 5), textcoords='offset points', 
                    fontsize=8, alpha=0.7)
    
    # 2. X坐标分布
    ax2.hist(corners[:, 0], bins=10, alpha=0.7, color='blue', edgecolor='black')
    ax2.set_title('X坐标分布直方图', fontsize=14)
    ax2.set_xlabel('X 坐标 (像素)')
    ax2.set_ylabel('频次')
    ax2.grid(True, alpha=0.3)
    
    # 3. Y坐标分布
    ax3.hist(corners[:, 1], bins=10, alpha=0.7, color='green', edgecolor='black')
    ax3.set_title('Y坐标分布直方图', fontsize=14)
    ax3.set_xlabel('Y 坐标 (像素)')
    ax3.set_ylabel('频次')
    ax3.grid(True, alpha=0.3)
    
    # 4. 角点网格结构 (5x4)
    corners_grid = corners.reshape(4, 5, 2)
    
    # 绘制网格线
    for row in range(4):
        ax4.plot(corners_grid[row, :, 0], corners_grid[row, :, 1], 'b-', alpha=0.7, linewidth=1)
    
    for col in range(5):
        ax4.plot(corners_grid[:, col, 0], corners_grid[:, col, 1], 'b-', alpha=0.7, linewidth=1)
    
    # 绘制角点
    ax4.scatter(corners[:, 0], corners[:, 1], c='red', s=50, zorder=5)
    
    ax4.set_title('棋盘格网格结构 (5x4)', fontsize=14)
    ax4.set_xlabel('X 坐标 (像素)')
    ax4.set_ylabel('Y 坐标 (像素)')
    ax4.grid(True, alpha=0.3)
    ax4.invert_yaxis()
    
    plt.tight_layout()
    plt.savefig('chessboard_corner_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("角点分析图表已保存为 'chessboard_corner_analysis.png'")

if __name__ == "__main__":
    print("棋盘格角点检测结果分析")
    print("=" * 50)
    
    # 显示检测结果
    show_detection_results()
    
    # 创建分析图表
    create_corner_analysis_plot()
    
    print("\n分析完成！")
