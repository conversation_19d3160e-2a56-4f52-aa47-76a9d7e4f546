#!/usr/bin/env python3
"""
简单显示棋盘格角点检测结果
"""
import cv2
import numpy as np
import os

def main():
    """主函数"""
    print("棋盘格角点检测结果")
    print("=" * 40)
    
    # 读取角点坐标
    corners_file = "chessboard_corners_advanced.txt"
    if not os.path.exists(corners_file):
        print(f"角点文件不存在: {corners_file}")
        return
    
    # 读取角点坐标
    corners = np.loadtxt(corners_file, skiprows=2)
    print(f"成功读取 {len(corners)} 个角点坐标")
    
    # 显示前10个角点坐标
    print("\n检测到的棋盘格角点坐标 (前10个):")
    print("-" * 40)
    for i, (x, y) in enumerate(corners[:10]):
        print(f"角点 {i+1:2d}: ({x:7.2f}, {y:7.2f})")
    
    if len(corners) > 10:
        print(f"... 还有 {len(corners) - 10} 个角点")
    
    # 计算角点统计信息
    x_coords = corners[:, 0]
    y_coords = corners[:, 1]
    
    print(f"\n角点统计信息:")
    print("-" * 40)
    print(f"总角点数: {len(corners)}")
    print(f"X坐标范围: {x_coords.min():.2f} - {x_coords.max():.2f}")
    print(f"Y坐标范围: {y_coords.min():.2f} - {y_coords.max():.2f}")
    print(f"X坐标平均值: {x_coords.mean():.2f}")
    print(f"Y坐标平均值: {y_coords.mean():.2f}")
    
    # 分析棋盘格结构 (5x4 模式)
    print(f"\n棋盘格结构分析 (5x4 模式):")
    print("-" * 40)
    
    # 重新排列角点为5x4网格
    corners_grid = corners.reshape(4, 5, 2)
    
    # 计算行间距和列间距
    col_spacings = []
    row_spacings = []
    
    # 计算列间距 (水平方向)
    for row in range(4):
        for col in range(4):  # 5列，4个间距
            dx = corners_grid[row, col+1, 0] - corners_grid[row, col, 0]
            col_spacings.append(dx)
    
    # 计算行间距 (垂直方向)
    for row in range(3):  # 4行，3个间距
        for col in range(5):
            dy = corners_grid[row+1, col, 1] - corners_grid[row, col, 1]
            row_spacings.append(dy)
    
    print(f"列间距 (水平): {np.mean(col_spacings):.2f} ± {np.std(col_spacings):.2f} 像素")
    print(f"行间距 (垂直): {np.mean(row_spacings):.2f} ± {np.std(row_spacings):.2f} 像素")
    
    # 显示检测结果图像
    result_image = "chessboard_detection_results/chessboard_blurred_5x4.jpg"
    if os.path.exists(result_image):
        print(f"\n显示检测结果图像: {result_image}")
        img = cv2.imread(result_image)
        if img is not None:
            # 缩放显示
            height, width = img.shape[:2]
            if width > 1200 or height > 800:
                scale = min(1200/width, 800/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img_resized = cv2.resize(img, (new_width, new_height))
                
                cv2.imshow("Chessboard Corner Detection Result", img_resized)
                print(f"图像已缩放显示 (缩放比例: {scale:.2f})")
            else:
                cv2.imshow("Chessboard Corner Detection Result", img)
                print("图像原尺寸显示")
            
            print("按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        else:
            print("无法读取结果图像")
    else:
        print(f"结果图像不存在: {result_image}")
    
    print("\n检测完成！")
    print(f"角点坐标已保存在: {corners_file}")
    print("检测结果图像保存在: chessboard_detection_results/ 目录")

if __name__ == "__main__":
    main()
