import numpy as np
import math
from dataclasses import dataclass
from scipy.optimize import least_squares

# ---------- 旋转工具 ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# ---------- 内参 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)

    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

def intrinsics_from_hfov(w, h, hfov_deg, cx, cy):
    fovx = math.radians(hfov_deg)
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# ---------- 只存“固定参数”：内参 + 基线 ----------
@dataclass
class SystemFixed:
    Kp: np.ndarray      # 投影仪内参
    Kc: np.ndarray      # 相机内参
    dx: float           # 相机相对投影仪在 X 方向上的基线 (米)

# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, Kp, D):
    """
    u, v: (N,) 投影仪像素坐标
    D: 投影中心到墙面的距离 (m)
    返回: (N,3) 世界坐标点 (X,Y,0)
    """
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)
    dx, dy, dz = d

    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    t = D / dz
    P = Cp + d * t      # (3,N)
    return P.T          # (N,3)

# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, Kc, dx, D, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    dx: 相机中心相对投影仪在 X 方向上的基线 (m)
    D:  与墙面的距离
    返回: (N,2) 相机像素坐标
    """
    # 相机中心
    C_cam = np.array([dx, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy
    return np.vstack([u, v]).T   # (N,2)

# ---------- 棋盘格点 ----------
def make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=200):
    xs = np.linspace(margin, Wp - margin, nx)
    ys = np.linspace(margin, Hp - margin, ny)
    uu, vv = np.meshgrid(xs, ys)
    pts = np.vstack([uu.ravel(), vv.ravel()]).T
    return pts

# ---------- 残差函数：同时优化角度 + D ----------
def residual_params(theta, fixed: SystemFixed, proj_pts, cam_obs):
    """
    theta: [pitch, yaw, roll, D]
    fixed: 包含 Kp, Kc, dx
    proj_pts: (N,2)
    cam_obs:  (N,2)
    返回 (2N,) 残差
    """
    pitch, yaw, roll, D = theta
    Kp, Kc, dx = fixed.Kp, fixed.Kc, fixed.dx

    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(u_p, v_p, Kp, D)  # (N,3)

    # 墙面 3D -> 相机像素
    cam_pred = world_to_camera_pixel(Pw, Kc, dx, D, pitch, yaw, roll)  # (N,2)

    res = (cam_pred - cam_obs).ravel()
    return res

def main():
    # 基本参数
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp/2.0, Hp
    D_gt = 2.0                   # 真值 D

    Wc, Hc = 1920, 1080
    hfov_deg = 75.0
    cxc, cyc = Wc/2.0, Hc/2.0

    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)

    print('Kp:', Kp)

    Kc = intrinsics_from_hfov(Wc, Hc, hfov_deg, cxc, cyc)

    print('Kc:', Kc)

    exit(0)

    dx = 0.1   # 相机 x 方向基线，已知

    fixed = SystemFixed(Kp=Kp, Kc=Kc, dx=dx)

    # 真值角度
    pitch_gt = 8.0
    yaw_gt   = -5.0
    roll_gt  = 2.0

    print("Ground truth angles (deg):", pitch_gt, yaw_gt, roll_gt)
    print("Ground truth D (m):", D_gt)

    # 在投影仪 UI 上生成棋盘格
    proj_pts = make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=250)
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    # 用真值生成相机观测
    Pw = projector_pixel_to_world(proj_pts[:,0], proj_pts[:,1], Kp, D_gt)
    cam_obs = world_to_camera_pixel(Pw, Kc, dx, D_gt,
                                    pitch_gt, yaw_gt, roll_gt)

    # 加一点噪声
    noise_std = 0.3
    cam_obs_noisy = cam_obs + np.random.randn(*cam_obs.shape)*noise_std

    # 初始猜测：D 也给一个大概值就行
    theta0 = np.array([0.0, 0.0, 0.0, 1.5], dtype=np.float64)
    print("Initial guess [pitch,yaw,roll,D]:", theta0)

    # 给 D 加个约束：比如 0.5m ~ 4m
    lower = np.array([-30.0, -30.0, -30.0, 0.5])
    upper = np.array([ 30.0,  30.0,  30.0, 4.0])

    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs_noisy),
        method="trf",
        bounds=(lower, upper),
        verbose=1
    )

    pitch_est, yaw_est, roll_est, D_est = result.x
    print("\nEstimated angles (deg):", pitch_est, yaw_est, roll_est)
    print("Estimated D (m):", D_est)

    print("\nAngle error (deg):",
          [pitch_est - pitch_gt, yaw_est - yaw_gt, roll_est - roll_gt])
    print("D error (m):", D_est - D_gt)

    # 用估计值看看重投影误差（对无噪声 GT 点）
    cam_pred_est = world_to_camera_pixel(Pw, Kc, dx, D_est,
                                         pitch_est, yaw_est, roll_est)
    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print("Mean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

if __name__ == "__main__":
    main()