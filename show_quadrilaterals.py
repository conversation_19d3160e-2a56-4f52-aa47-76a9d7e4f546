#!/usr/bin/env python3
"""
显示生成的四边形图像
"""
import cv2
import os

def show_quadrilaterals_image():
    """显示四边形图像"""
    image_path = "quadrilaterals_output.jpg"
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 获取图像尺寸
    height, width = img.shape[:2]
    print(f"图像尺寸: {width} x {height}")
    
    # 如果图像太大，缩放显示
    max_display_width = 1200
    max_display_height = 800
    
    if width > max_display_width or height > max_display_height:
        # 计算缩放比例
        scale_w = max_display_width / width
        scale_h = max_display_height / height
        scale = min(scale_w, scale_h)
        
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        img_resized = cv2.resize(img, (new_width, new_height))
        print(f"图像已缩放至: {new_width} x {new_height} (缩放比例: {scale:.2f})")
        
        cv2.imshow("Quadrilaterals (Resized)", img_resized)
    else:
        cv2.imshow("Quadrilaterals", img)
    
    print("按任意键关闭窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    show_quadrilaterals_image()
