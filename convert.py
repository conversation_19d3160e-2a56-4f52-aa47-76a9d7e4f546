import cv2
import numpy as np
import argparse
import os
import math

def nv12_to_bgr(input_path, output_path, width, height):
    """
    将 NV12 二进制文件转换为 BGR 格式的图片
    :param input_path: 输入的 NV12 文件路径
    :param output_path: 输出的 BGR 图片路径
    :param width: 图像宽度 (3840)
    :param height: 图像高度 (2160)
    """
    try:
        # 计算 NV12 文件预期大小
        expected_size = width * height * 3 // 2
        
        # 读取二进制数据
        with open(input_path, 'rb') as f:
            nv12_data = f.read()
        
        # 验证文件大小
        if len(nv12_data) != expected_size:
            raise ValueError(f"文件大小错误: 预期 {expected_size} 字节, 实际 {len(nv12_data)} 字节")
        
        # 转换为 numpy 数组并重塑形状
        nv12_array = np.frombuffer(nv12_data, dtype=np.uint8)
        bgr_image = nv12_array.reshape((height * 3 // 2, width,1))  # 关键步骤：NV12 格式的特殊形状
        bgr_image = cv2.cvtColor(bgr_image, cv2.COLOR_YUV2BGR_NV12)  # 关键步骤：颜色空间转换
        
        # 执行颜色空间转换
 
        # 保存结果
        cv2.imwrite(output_path, bgr_image)
        print(f"转换成功! 结果已保存至: {output_path}")
        
        return True
    
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def calculate_angle(p1, p2, p3):
    """
    计算由三个点组成的角度（以p2为顶点）
    :param p1: 第一个点 (x, y)
    :param p2: 顶点 (x, y)
    :param p3: 第三个点 (x, y)
    :return: 角度（度数）
    """
    # 计算向量
    v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
    v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

    # 计算向量的模长
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # 避免除零错误
    if norm_v1 == 0 or norm_v2 == 0:
        return 0

    # 计算余弦值
    cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)

    # 限制余弦值在[-1, 1]范围内，避免数值误差
    cos_angle = np.clip(cos_angle, -1, 1)

    # 计算角度（弧度转度数）
    angle_rad = np.arccos(cos_angle)
    angle_deg = np.degrees(angle_rad)

    return angle_deg

def calculate_quadrilateral_angles(points):
    """
    计算四边形的四个内角
    :param points: 四个点的列表，每个点为(x, y)元组，按顺序排列
    :return: 四个角度的列表（度数）
    """
    if len(points) != 4:
        raise ValueError("必须提供4个点")

    angles = []
    n = len(points)

    # 计算每个顶点的内角
    for i in range(n):
        # 获取当前顶点和相邻的两个顶点
        prev_point = points[(i - 1) % n]  # 前一个点
        current_point = points[i]         # 当前点
        next_point = points[(i + 1) % n]  # 下一个点

        # 计算角度
        angle = calculate_angle(prev_point, current_point, next_point)
        angles.append(angle)

    return angles

def print_quadrilateral_info(points):
    """
    打印四边形的详细信息
    :param points: 四个点的列表
    """
    print("四边形顶点坐标:")
    for i, point in enumerate(points):
        print(f"  点{i+1}: ({point[0]:.2f}, {point[1]:.2f})")

    angles = calculate_quadrilateral_angles(points)

    print("\n四边形内角:")
    for i, angle in enumerate(angles):
        print(f"  角{i+1}: {angle:.2f}°")

    print(f"\n内角和: {sum(angles):.2f}° (理论值应为360°)")

    return angles

def draw_multiple_quadrilaterals(image_path, quadrilaterals_list, output_path=None):
    """
    在同一张图像上绘制多个四边形
    :param image_path: 输入图像路径
    :param quadrilaterals_list: 四边形列表，每个四边形包含4个点的坐标
    :param output_path: 输出图像路径，如果为None则显示图像
    """
    # 读取图像
    if isinstance(image_path, str):
        img = cv2.imread(image_path)
        if img is None:
            print(f"无法读取图像: {image_path}")
            return None
    else:
        img = image_path.copy()

    # 定义颜色列表（BGR格式）
    colors = [
        (0, 0, 255),    # 红色
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 255, 255),  # 黄色
        (255, 0, 255),  # 品红色
        (255, 255, 0),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]

    # 绘制每个四边形
    for i, points in enumerate(quadrilaterals_list):
        if len(points) != 4:
            print(f"警告: 第{i+1}个四边形点数不正确，跳过")
            continue

        # 选择颜色
        color = colors[i % len(colors)]

        # 转换点格式为numpy数组
        pts = np.array(points, np.int32)
        pts = pts.reshape((-1, 1, 2))

        # 绘制四边形轮廓
        cv2.polylines(img, [pts], True, color, 2)

        # 在每个顶点绘制小圆点
        for j, point in enumerate(points):
            cv2.circle(img, (int(point[0]), int(point[1])), 3, color, -1)
            # 添加点的标签
            cv2.putText(img, f"{i+1}-{j+1}", (int(point[0])+5, int(point[1])-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 在四边形中心添加编号
        center_x = sum(p[0] for p in points) // 4
        center_y = sum(p[1] for p in points) // 4
        cv2.putText(img, f"Quad{i+1}", (center_x-20, center_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # 保存或显示图像
    if output_path:
        cv2.imwrite(output_path, img)
        print(f"绘制完成，结果已保存至: {output_path}")
    else:
        cv2.imshow("Multiple Quadrilaterals", img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

    return img

def detect_chessboard_corners(image_path, pattern_size=(9, 6), output_path=None):
    """
    检测棋盘格角点
    :param image_path: 输入图像路径
    :param pattern_size: 棋盘格内角点数量 (width, height)，默认为(9,6)
    :param output_path: 输出图像路径，如果为None则显示图像
    :return: (ret, corners, img_with_corners) - 是否找到角点、角点坐标、标记了角点的图像
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return False, None, None

    print(f"图像尺寸: {img.shape[1]} x {img.shape[0]}")

    # 转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 设置角点检测的终止条件
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)

    # 检测棋盘格角点
    print(f"正在检测棋盘格角点，模式: {pattern_size}")
    ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)

    if ret:
        print(f"成功检测到 {len(corners)} 个角点")

        # 亚像素精度优化角点位置
        corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)

        # 在图像上绘制角点
        img_with_corners = img.copy()
        cv2.drawChessboardCorners(img_with_corners, pattern_size, corners_refined, ret)

        # 打印前几个角点的坐标
        print("\n前10个角点坐标:")
        for i, corner in enumerate(corners_refined[:10]):
            x, y = corner.ravel()
            print(f"角点 {i+1}: ({x:.2f}, {y:.2f})")

        if len(corners_refined) > 10:
            print(f"... 还有 {len(corners_refined) - 10} 个角点")

        # 保存或显示结果
        if output_path:
            cv2.imwrite(output_path, img_with_corners)
            print(f"结果已保存至: {output_path}")
        else:
            # 如果图像太大，缩放显示
            height, width = img_with_corners.shape[:2]
            max_display_width = 1200
            max_display_height = 800

            if width > max_display_width or height > max_display_height:
                scale_w = max_display_width / width
                scale_h = max_display_height / height
                scale = min(scale_w, scale_h)

                new_width = int(width * scale)
                new_height = int(height * scale)

                img_resized = cv2.resize(img_with_corners, (new_width, new_height))
                cv2.imshow("Chessboard Corners (Resized)", img_resized)
                print(f"图像已缩放显示 (缩放比例: {scale:.2f})")
            else:
                cv2.imshow("Chessboard Corners", img_with_corners)

            print("按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        return True, corners_refined, img_with_corners

    else:
        print("未能检测到棋盘格角点")
        print("可能的原因:")
        print("1. 棋盘格模式大小不正确")
        print("2. 图像质量不佳或模糊")
        print("3. 棋盘格不完整或被遮挡")
        print("4. 光照条件不理想")

        return False, None, img

def try_different_chessboard_patterns(image_path):
    """
    尝试不同的棋盘格模式来检测角点
    :param image_path: 输入图像路径
    """
    # 常见的棋盘格模式
    common_patterns = [
        (9, 6),   # 10x7 棋盘格的内角点
        (8, 6),   # 9x7 棋盘格的内角点
        (7, 5),   # 8x6 棋盘格的内角点
        (6, 4),   # 7x5 棋盘格的内角点
        (9, 7),   # 10x8 棋盘格的内角点
        (8, 5),   # 9x6 棋盘格的内角点
        (10, 7),  # 11x8 棋盘格的内角点
        (11, 8),  # 12x9 棋盘格的内角点
    ]

    print(f"尝试检测图像: {image_path}")
    print("=" * 60)

    for i, pattern in enumerate(common_patterns):
        print(f"\n尝试模式 {i+1}: {pattern[0]}x{pattern[1]} 内角点")
        ret, corners, img_with_corners = detect_chessboard_corners(
            image_path, pattern, f"chessboard_corners_pattern_{pattern[0]}x{pattern[1]}.jpg"
        )

        if ret:
            print(f"✓ 成功！使用模式 {pattern}")
            return ret, corners, img_with_corners, pattern
        else:
            print(f"✗ 失败")

    print("\n所有常见模式都未能检测到角点")
    return False, None, None, None

if __name__ == "__main__":
    # 棋盘格角点检测
    image_path = r"D:\Dataset\keystone\cam_1080p_150cm_10_r_no.jpg"

    # 检查图像文件是否存在
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        print("请检查文件路径是否正确")
        quit()

    print("开始检测棋盘格角点...")
    print("=" * 50)

    # 方法1: 尝试默认的9x6模式
    print("方法1: 使用默认模式 (9x6)")
    ret, corners, img_with_corners = detect_chessboard_corners(
        image_path, (9, 6), "chessboard_corners_default.jpg"
    )

    if not ret:
        print("\n默认模式失败，尝试其他常见模式...")
        print("=" * 50)

        # 方法2: 尝试多种常见模式
        ret, corners, img_with_corners, best_pattern = try_different_chessboard_patterns(image_path)

        if ret:
            print(f"\n最终成功检测到角点！最佳模式: {best_pattern}")

            # 保存角点坐标到文件
            if corners is not None:
                corners_array = corners.reshape(-1, 2)
                np.savetxt("chessboard_corners.txt", corners_array, fmt="%.2f",
                          header=f"Chessboard corners detected with pattern {best_pattern}\nX Y")
                print(f"角点坐标已保存到 'chessboard_corners.txt'")
        else:
            print("\n所有尝试都失败了。建议:")
            print("1. 检查图像中是否包含完整的棋盘格")
            print("2. 确认棋盘格的行列数")
            print("3. 检查图像质量和光照条件")
    else:
        print("成功检测到角点！")

        # 保存角点坐标到文件
        if corners is not None:
            corners_array = corners.reshape(-1, 2)
            np.savetxt("chessboard_corners.txt", corners_array, fmt="%.2f",
                      header="Chessboard corners detected with pattern (9,6)\nX Y")
            print(f"角点坐标已保存到 'chessboard_corners.txt'")

    quit()
    # 创建一个空白图像来绘制四边形
    # 根据坐标范围确定图像大小
    max_x = max(max(point[0] for point in quad) for quad in quadrilaterals)
    max_y = max(max(point[1] for point in quad) for quad in quadrilaterals)

    # 创建白色背景图像，留一些边距
    img_width = max_x + 100
    img_height = max_y + 100
    blank_img = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255

    # 绘制所有四边形
    result_img = draw_multiple_quadrilaterals(blank_img, quadrilaterals, "quadrilaterals_output.jpg")

    # 打印每个四边形的信息
    for i, quad in enumerate(quadrilaterals):
        print(f"\n=== 四边形 {i+1} ===")
        print_quadrilateral_info(quad)

    print(f"\n总共绘制了 {len(quadrilaterals)} 个四边形")
    print("图像已保存为 'quadrilaterals_output.jpg'")

    # 如果你想使用现有的图像作为背景，可以取消下面的注释并修改路径
    # img_path = r"D:\Dataset\keystone\cam_1080p_150cm_20_r_no.jpg"
    # if os.path.exists(img_path):
    #     result_img = draw_multiple_quadrilaterals(img_path, quadrilaterals, "quadrilaterals_on_image.jpg")

    quit()

    # 如果需要运行NV12转换代码，请取消下面的注释
    width = 1280
    height = 720
    srcdir = r"D:\imgs"
    dstdir = r"\imgs\rgb"
    os.makedirs(dstdir, exist_ok=True)
    for fn in os.listdir(srcdir):
        nv12_to_bgr(os.path.join(srcdir,fn), os.path.join(dstdir,fn.replace(".bin",".bmp")), width, height)