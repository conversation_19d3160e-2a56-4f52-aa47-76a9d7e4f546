###

正投时
标定求解摄像头相对光机的旋转角： pitch, yaw, roll
(需要已知投影光机内参、相机内参，摄像头安装在光机水平右侧x=0.01   可解  pitch, yaw, roll, D)
相关模拟验证见：
[ba_calculate_angle_and_d.py](ba_calculate_angle_and_d.py)

相关实际求解见：
[calib_real_at_0_angle.py](calib_real_at_0_angle.py)
[calib_real_at_0_angle_2.py](calib_real_at_0_angle_2.py)

侧投时
已知摄像头Kc 内参， 光机 Kp内参， 以及 摄像头相对光机的 pitch, yaw, roll 以及中心位置差异
可求解整机姿态角
相关模拟验证见：
[ba_3_angle_runtime.py](ba_3_angle_runtime.py)

问题：
还没有测试实际求解整机姿态角， 但发现单 H 矩阵映射的像素误差就有 2.4 pix 见 calib_real_at_0_angle.py 196 行
？ 畸变引入的吗，还是说墙不平
或可先参考 ba_3_angle_runtime.py 严重下实际数据求解整机姿态角试试

待确认：
投影光机内参的准确性（应该是较准的， 光机一般规格离理论值偏差非常小）
相机内参的准确性
