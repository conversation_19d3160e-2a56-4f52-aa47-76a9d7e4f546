#!/usr/bin/env python3
"""
分析四边形的特征和关系
"""
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
import matplotlib.patches as patches

def calculate_area(points):
    """使用鞋带公式计算四边形面积"""
    x = [p[0] for p in points]
    y = [p[1] for p in points]
    return 0.5 * abs(sum(x[i]*y[i+1] - x[i+1]*y[i] for i in range(-1, len(x)-1)))

def calculate_perimeter(points):
    """计算四边形周长"""
    perimeter = 0
    for i in range(len(points)):
        p1 = points[i]
        p2 = points[(i + 1) % len(points)]
        distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        perimeter += distance
    return perimeter

def calculate_center(points):
    """计算四边形中心点"""
    x_center = sum(p[0] for p in points) / len(points)
    y_center = sum(p[1] for p in points) / len(points)
    return (x_center, y_center)

def analyze_quadrilaterals():
    """分析所有四边形"""
    # 定义8个四边形的坐标点
    quadrilaterals = [
        [(186, 109), (1078, 126), (1155, 650), (90, 636)],
        [(193, 110), (1085, 127), (1163, 650), (100, 636)],
        [(200, 110), (1090, 127), (1168, 648), (108, 636)],
        [(205, 111), (1094, 127), (1174, 649), (115, 636)],
        [(211, 110), (1099, 128), (1179, 649), (119, 636)],
        [(213, 111), (1101, 129), (1183, 650), (124, 636)],
        [(216, 111), (1104, 129), (1187, 650), (128, 637)],
        [(219, 112), (1107, 129), (1190, 650), (132, 637)]
    ]
    
    # 分析每个四边形
    print("四边形分析结果:")
    print("=" * 80)
    
    areas = []
    perimeters = []
    centers = []
    
    for i, quad in enumerate(quadrilaterals):
        area = calculate_area(quad)
        perimeter = calculate_perimeter(quad)
        center = calculate_center(quad)
        
        areas.append(area)
        perimeters.append(perimeter)
        centers.append(center)
        
        print(f"四边形 {i+1}:")
        print(f"  面积: {area:.2f} 平方像素")
        print(f"  周长: {perimeter:.2f} 像素")
        print(f"  中心点: ({center[0]:.1f}, {center[1]:.1f})")
        print()
    
    # 统计分析
    print("统计分析:")
    print("=" * 40)
    print(f"平均面积: {np.mean(areas):.2f} ± {np.std(areas):.2f}")
    print(f"平均周长: {np.mean(perimeters):.2f} ± {np.std(perimeters):.2f}")
    print(f"面积范围: {min(areas):.2f} - {max(areas):.2f}")
    print(f"周长范围: {min(perimeters):.2f} - {max(perimeters):.2f}")
    
    # 分析中心点的移动趋势
    print("\n中心点移动分析:")
    print("=" * 40)
    for i in range(1, len(centers)):
        dx = centers[i][0] - centers[i-1][0]
        dy = centers[i][1] - centers[i-1][1]
        distance = np.sqrt(dx**2 + dy**2)
        print(f"四边形{i} -> 四边形{i+1}: dx={dx:.1f}, dy={dy:.1f}, 距离={distance:.1f}")
    
    return quadrilaterals, areas, perimeters, centers

def plot_quadrilaterals_analysis():
    """绘制四边形分析图"""
    quadrilaterals, areas, perimeters, centers = analyze_quadrilaterals()
    
    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 绘制所有四边形
    ax1.set_title('所有四边形叠加图', fontsize=14)
    colors = plt.cm.tab10(np.linspace(0, 1, len(quadrilaterals)))
    
    for i, (quad, color) in enumerate(zip(quadrilaterals, colors)):
        polygon = Polygon(quad, fill=False, edgecolor=color, linewidth=2, label=f'四边形{i+1}')
        ax1.add_patch(polygon)
        
        # 标记中心点
        center = centers[i]
        ax1.plot(center[0], center[1], 'o', color=color, markersize=6)
        ax1.text(center[0]+10, center[1]+10, f'{i+1}', fontsize=10, color=color)
    
    ax1.set_xlim(0, 1300)
    ax1.set_ylim(0, 700)
    ax1.invert_yaxis()  # 图像坐标系
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 2. 面积变化
    ax2.set_title('四边形面积变化', fontsize=14)
    ax2.plot(range(1, len(areas)+1), areas, 'bo-', linewidth=2, markersize=8)
    ax2.set_xlabel('四边形编号')
    ax2.set_ylabel('面积 (平方像素)')
    ax2.grid(True, alpha=0.3)
    
    # 3. 周长变化
    ax3.set_title('四边形周长变化', fontsize=14)
    ax3.plot(range(1, len(perimeters)+1), perimeters, 'ro-', linewidth=2, markersize=8)
    ax3.set_xlabel('四边形编号')
    ax3.set_ylabel('周长 (像素)')
    ax3.grid(True, alpha=0.3)
    
    # 4. 中心点轨迹
    ax4.set_title('四边形中心点轨迹', fontsize=14)
    x_centers = [c[0] for c in centers]
    y_centers = [c[1] for c in centers]
    
    ax4.plot(x_centers, y_centers, 'go-', linewidth=2, markersize=8, label='中心点轨迹')
    for i, (x, y) in enumerate(centers):
        ax4.text(x+5, y+5, f'{i+1}', fontsize=10)
    
    ax4.set_xlabel('X 坐标')
    ax4.set_ylabel('Y 坐标')
    ax4.invert_yaxis()  # 图像坐标系
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('quadrilaterals_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n分析图表已保存为 'quadrilaterals_analysis.png'")

if __name__ == "__main__":
    plot_quadrilaterals_analysis()
