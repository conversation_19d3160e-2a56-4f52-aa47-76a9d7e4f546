#!/usr/bin/env python3
"""
高级棋盘格角点检测
包含图像预处理和多种检测方法
"""
import cv2
import numpy as np
import os
import matplotlib.pyplot as plt

def preprocess_image(img):
    """
    图像预处理以提高角点检测成功率
    """
    # 转换为灰度图
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img.copy()
    
    # 应用高斯模糊减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 直方图均衡化
    equalized = cv2.equalizeHist(blurred)
    
    # 自适应阈值
    adaptive_thresh = cv2.adaptiveThreshold(
        equalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
    )
    
    return gray, blurred, equalized, adaptive_thresh

def detect_chessboard_advanced(image_path, output_dir="chessboard_detection_results"):
    """
    高级棋盘格检测，包含多种预处理和检测方法
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return False, None, None
    
    print(f"图像尺寸: {img.shape[1]} x {img.shape[0]}")
    
    # 图像预处理
    gray, blurred, equalized, adaptive_thresh = preprocess_image(img)
    
    # 保存预处理结果
    cv2.imwrite(os.path.join(output_dir, "01_original.jpg"), img)
    cv2.imwrite(os.path.join(output_dir, "02_gray.jpg"), gray)
    cv2.imwrite(os.path.join(output_dir, "03_blurred.jpg"), blurred)
    cv2.imwrite(os.path.join(output_dir, "04_equalized.jpg"), equalized)
    cv2.imwrite(os.path.join(output_dir, "05_adaptive_thresh.jpg"), adaptive_thresh)
    
    # 设置角点检测参数
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
    
    # 扩展的棋盘格模式列表
    patterns = [
        (9, 6), (8, 6), (7, 5), (6, 4), (9, 7), (8, 5), (10, 7), (11, 8),
        (5, 4), (4, 3), (6, 5), (7, 6), (8, 7), (10, 6), (12, 8), (13, 9),
        (6, 3), (7, 4), (8, 4), (9, 5), (10, 5), (11, 6), (12, 7), (14, 10)
    ]
    
    # 尝试不同的预处理图像
    test_images = [
        ("gray", gray),
        ("blurred", blurred), 
        ("equalized", equalized),
        ("adaptive_thresh", adaptive_thresh)
    ]
    
    print("开始尝试不同的预处理方法和棋盘格模式...")
    print("=" * 60)
    
    best_result = None
    max_corners = 0
    
    for img_name, test_img in test_images:
        print(f"\n使用 {img_name} 图像进行检测:")
        print("-" * 40)
        
        for i, pattern in enumerate(patterns):
            ret, corners = cv2.findChessboardCorners(test_img, pattern, None)
            
            if ret:
                # 亚像素精度优化
                corners_refined = cv2.cornerSubPix(test_img, corners, (11, 11), (-1, -1), criteria)
                
                num_corners = len(corners_refined)
                print(f"✓ 模式 {pattern}: 检测到 {num_corners} 个角点")
                
                # 记录最佳结果
                if num_corners > max_corners:
                    max_corners = num_corners
                    best_result = {
                        'pattern': pattern,
                        'corners': corners_refined,
                        'image_type': img_name,
                        'test_img': test_img,
                        'original_img': img
                    }
                
                # 绘制并保存结果
                img_with_corners = img.copy()
                cv2.drawChessboardCorners(img_with_corners, pattern, corners_refined, ret)
                
                output_filename = f"chessboard_{img_name}_{pattern[0]}x{pattern[1]}.jpg"
                cv2.imwrite(os.path.join(output_dir, output_filename), img_with_corners)
            else:
                if i < 5:  # 只显示前几个失败的尝试
                    print(f"✗ 模式 {pattern}: 失败")
    
    return best_result

def harris_corner_detection(image_path, output_dir="corner_detection_results"):
    """
    使用Harris角点检测作为备选方案
    """
    os.makedirs(output_dir, exist_ok=True)
    
    img = cv2.imread(image_path)
    if img is None:
        return False, None
    
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Harris角点检测
    harris_corners = cv2.cornerHarris(gray, 2, 3, 0.04)
    
    # 标记角点
    img_harris = img.copy()
    img_harris[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]
    
    cv2.imwrite(os.path.join(output_dir, "harris_corners.jpg"), img_harris)
    
    # 获取角点坐标
    corners = np.where(harris_corners > 0.01 * harris_corners.max())
    corner_coords = list(zip(corners[1], corners[0]))  # (x, y) 格式
    
    print(f"Harris角点检测找到 {len(corner_coords)} 个角点")
    
    return True, corner_coords

if __name__ == "__main__":
    image_path = r"D:\Dataset\keystone\cam_1080p_150cm_10_r_no.jpg"
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        quit()
    
    print("高级棋盘格角点检测")
    print("=" * 50)
    
    # 方法1: 高级棋盘格检测
    result = detect_chessboard_advanced(image_path)
    
    if result:
        print(f"\n成功检测到棋盘格！")
        print(f"最佳模式: {result['pattern']}")
        print(f"使用图像: {result['image_type']}")
        print(f"角点数量: {len(result['corners'])}")
        
        # 保存角点坐标
        corners_array = result['corners'].reshape(-1, 2)
        np.savetxt("chessboard_corners_advanced.txt", corners_array, fmt="%.2f",
                  header=f"Chessboard corners - Pattern: {result['pattern']}, Image: {result['image_type']}\nX Y")
        print("角点坐标已保存到 'chessboard_corners_advanced.txt'")
    else:
        print("\n棋盘格检测失败，尝试Harris角点检测...")
        
        # 方法2: Harris角点检测
        success, harris_corners = harris_corner_detection(image_path)
        
        if success and harris_corners:
            print(f"Harris角点检测成功，找到 {len(harris_corners)} 个角点")
            
            # 保存Harris角点
            np.savetxt("harris_corners.txt", harris_corners, fmt="%.0f",
                      header="Harris corner detection results\nX Y")
            print("Harris角点坐标已保存到 'harris_corners.txt'")
        else:
            print("所有角点检测方法都失败了")
    
    print("\n检测完成！请查看生成的结果文件和图像。")
