import numpy as np
import math
from dataclasses import dataclass
from scipy.optimize import least_squares

# ---------- 旋转工具（沿用你原来的） ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation (和你之前一致)
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# ---------- 内参计算 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

def intrinsics_from_hfov(w, h, hfov_deg, cx, cy):
    fovx = math.radians(hfov_deg)
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# ---------- 数据结构 ----------
@dataclass
class SystemParams:
    Kp: np.ndarray      # 投影仪内参
    Kc: np.ndarray      # 相机内参
    D: float            # 投影中心到墙面的距离 (m)
    C_cam: np.ndarray   # 相机中心 (world coords), shape (3,)

# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, params: SystemParams):
    """
    u, v: 标量或 1D array，投影仪像素坐标
    返回形状 (N, 3) 的世界坐标点 (X,Y,Z=0)
    """
    Kp = params.Kp
    D = params.D

    # 构造齐次像素
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)

    # 射线方向（投影仪坐标 == 世界坐标）
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)
    dx, dy, dz = d      # each (N,)

    # 投影仪中心
    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    # 和 Z=0 平面交点
    t = D / dz          # (N,)
    P = Cp + d * t      # (3,N)
    return P.T          # (N,3)

# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, params: SystemParams, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    返回: (N,2) 相机像素坐标
    """
    Kc = params.Kc
    C_cam = params.C_cam.reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    # 转到相机坐标
    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy

    return np.vstack([u, v]).T   # (N,2)

# ---------- 构造投影仪上的棋盘格 ----------
def make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=200):
    """
    在投影仪图像上生成一个 nx * ny 的角点网格
    返回: (N,2) 像素坐标 (u_p, v_p)
    """
    # 在可见区域中均匀排布棋盘格
    xs = np.linspace(margin, Wp - margin, nx)
    ys = np.linspace(margin, Hp - margin, ny)
    uu, vv = np.meshgrid(xs, ys)
    pts = np.vstack([uu.ravel(), vv.ravel()]).T
    return pts

# ---------- 残差函数 ----------
def residual_angles(theta, params: SystemParams, proj_pts, cam_obs):
    """
    theta: [pitch, yaw, roll] (deg)
    proj_pts: (N,2), 投影仪像素坐标
    cam_obs:  (N,2), 相机观测像素坐标
    返回: (2N,) 的残差向量
    """
    pitch, yaw, roll = theta
    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 1) 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(u_p, v_p, params)   # (N,3)

    # 2) 墙面 3D -> 相机像素（预测）
    cam_pred = world_to_camera_pixel(Pw, params, pitch, yaw, roll)  # (N,2)

    # 3) 残差
    res = (cam_pred - cam_obs).ravel()
    return res

def main():
    # ----------- 基本参数（与你之前代码保持一致） -----------
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp / 2.0, Hp      # 主点在底边中心
    D_m = 2.0                    # 投影中心到墙面的距离（米）

    Wc, Hc = 1920, 1080
    hfov_deg = 75.0
    cxc, cyc = Wc / 2.0, Hc / 2.0

    # 投影仪内参、相机内参
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    Kc = intrinsics_from_hfov(Wc, Hc, hfov_deg, cxc, cyc)

    # 相机中心（相对投影仪）：已知
    C_cam = np.array([0.1, 0.0, -D_m], dtype=np.float64)

    params = SystemParams(Kp=Kp, Kc=Kc, D=D_m, C_cam=C_cam)

    # ----------- 设定“真值”角度，用于模拟 ----------- 
    pitch_gt = 8.0   # deg
    yaw_gt   = -5.0
    roll_gt  = 2.0
    print("Ground truth angles (deg):", pitch_gt, yaw_gt, roll_gt)

    # ----------- 在投影仪 UI 上生成棋盘格点 -----------
    proj_pts = make_projector_chessboard(Wp, Hp, nx=8, ny=5, margin=250)  # (N,2)
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    # ----------- 用真值角度生成相机观测 -----------
    # 1) 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(proj_pts[:,0], proj_pts[:,1], params)  # (N,3)

    # 2) 墙面 3D -> 相机像素（“观测值”）
    cam_obs = world_to_camera_pixel(Pw, params, pitch_gt, yaw_gt, roll_gt)  # (N,2)

    # 添加一点高斯噪声，模拟角点检测误差（可选）
    noise_std = 0.3  # 像素级
    cam_obs_noisy = cam_obs + np.random.randn(*cam_obs.shape) * noise_std

    # ----------- 优化求解 pitch, yaw, roll -----------
    theta0 = np.array([0.0, 0.0, 0.0], dtype=np.float64)  # 初始猜测

    print("Initial guess (deg):", theta0)

    result = least_squares(
        residual_angles,
        theta0,
        args=(params, proj_pts, cam_obs_noisy),
        method='lm',     # Levenberg–Marquardt
        verbose=1
    )

    pitch_est, yaw_est, roll_est = result.x
    print("\nEstimated angles (deg):", pitch_est, yaw_est, roll_est)

    # ----------- 误差评估 -----------
    angle_err = np.array([pitch_est - pitch_gt,
                          yaw_est   - yaw_gt,
                          roll_est  - roll_gt])
    print("Angle error (deg):", angle_err)

    # 用估计角度再算一次重投影误差
    cam_pred_est = world_to_camera_pixel(Pw, params, pitch_est, yaw_est, roll_est)
    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)  # (N,)
    print("Mean reprojection error (no-noise GT) [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

if __name__ == "__main__":
    main()